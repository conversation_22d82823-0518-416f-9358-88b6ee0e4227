{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Narrative TTRPG Content Schema", "description": "Defines the structure for game-agnostic narrative YAML frontmatter in TTRPG Markdown files.", "type": "object", "definitions": {"universal_narrative_fields": {"type": "object", "properties": {"id": {"type": "string", "description": "Globally unique identifier, typically constructed as: adventure_slug.[entity_type_specific_prefix_if_any.]name (e.g., tlom.adventure_section.the-oubliette)"}, "entity_type": {"type": "string", "enum": ["campaign", "adventure", "adventure_section", "chapter", "location", "npc", "faction", "event", "quest", "item_narrative", "lore", "timeline_entry"], "description": "The type of narrative TTRPG entity."}, "name": {"type": "string", "description": "Human-readable name of the entity, often used as the last part of the filename and ID (e.g., 'the-oubliette', 'elara'). Should be slugified for IDs/filenames."}, "game_system": {"type": "string", "description": "Specifies the primary TTRPG system this narrative entity is intended for (e.g., 'dnd5e', 'pf2e', 'universal_narrative'). While the entity's structure is defined by this narrative schema, this field indicates the module's target system."}, "adventure_slug": {"type": "string", "description": "Short, filesystem-safe slug for the adventure this entity belongs to (e.g., 'tlom')."}, "organizational_hierarchy": {"type": "object", "description": "Defines the hierarchical position of this entity within the larger organizational structure", "properties": {"campaign_id": {"type": ["string", "null"], "description": "ID of the parent campaign this entity belongs to (e.g., 'curse-of-strahd-campaign')"}, "module_id": {"type": ["string", "null"], "description": "ID of the parent module/adventure this entity belongs to (e.g., 'death-house', 'village-of-barovia')"}, "chapter_id": {"type": ["string", "null"], "description": "ID of the chapter within the module (e.g., 'chapter-1-into-the-mists')"}, "section_id": {"type": ["string", "null"], "description": "ID of the section within the chapter (e.g., 'section-a-village-approach')"}, "subsection_id": {"type": ["string", "null"], "description": "ID of the subsection for fine-grained organization (e.g., 'room-1-entrance-hall')"}}}, "source_book": {"type": "string", "description": "Original sourcebook or material, if applicable."}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Keywords for searching and categorization."}, "aliases": {"type": "array", "items": {"type": "string"}, "description": "Alternative names or nicknames."}, "summary": {"type": "string", "description": "A brief one or two-sentence description of this narrative element."}, "relationships": {"type": "object", "description": "Defines relationships between this entity and other entities in the system", "properties": {"contains": {"type": "array", "items": {"type": "string"}, "description": "IDs of entities that this entity contains (e.g., an adventure contains locations, NPCs)"}, "contained_by": {"type": ["string", "null"], "description": "ID of the entity that contains this entity"}, "references": {"type": "array", "items": {"type": "string"}, "description": "IDs of entities that this entity references or mentions"}, "referenced_by": {"type": "array", "items": {"type": "string"}, "description": "IDs of entities that reference this entity"}, "depends_on": {"type": "array", "items": {"type": "string"}, "description": "IDs of entities that this entity depends on for context or completion"}, "enables": {"type": "array", "items": {"type": "string"}, "description": "IDs of entities that this entity enables or unlocks"}}}, "content_organization": {"type": "object", "description": "Metadata for organizing and presenting content", "properties": {"display_order": {"type": ["integer", "null"], "description": "Numeric order for displaying this entity among siblings"}, "content_type": {"type": "string", "enum": ["narrative", "mechanical", "reference", "background", "plot", "encounter"], "description": "The primary type of content this entity represents"}, "complexity_level": {"type": "string", "enum": ["simple", "moderate", "complex", "advanced"], "description": "Indicates the complexity level for GM preparation"}, "estimated_duration_minutes": {"type": ["integer", "null"], "description": "Estimated time in minutes for this content to be experienced in play"}, "prerequisites": {"type": "array", "items": {"type": "string"}, "description": "IDs of entities that should be completed or understood before this one"}}}}, "required": ["id", "entity_type", "name", "game_system", "adventure_slug"]}, "adventure_yaml": {"allOf": [{"$ref": "#/definitions/universal_narrative_fields"}], "type": "object", "properties": {"entity_type": {"const": "adventure"}, "adventure_title": {"type": "string", "description": "Full human-readable title of the adventure (e.g., 'The Light of Memoria'). The 'name' field from universal_narrative_fields would be the slug (e.g., 'the-light-of-memoria')."}, "tagline": {"type": "string", "description": "A catchy tagline for the adventure."}, "intended_pc_level_narrative": {"type": "string", "description": "Narrative description of intended player character level or experience."}, "authors": {"type": "array", "items": {"type": "string"}}, "setting_overview": {"type": "string", "description": "Brief overview of the adventure's setting."}, "plot_hook": {"type": "string", "description": "Initial plot hook or introduction for players."}, "plot_summary_narrative": {"type": "string", "description": "Overall summary of the adventure's plot."}, "key_factions_narrative": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "goals_narrative": {"type": "string"}, "linked_faction_id": {"type": ["string", "null"], "description": "Optional ID to a more detailed faction entity if one exists."}}}}, "major_themes": {"type": "array", "items": {"type": "string"}}}, "required": ["adventure_title", "plot_summary_narrative"]}, "adventure_section_yaml": {"allOf": [{"$ref": "#/definitions/universal_narrative_fields"}], "type": "object", "properties": {"entity_type": {"const": "adventure_section"}, "parent_adventure_id": {"type": "string", "description": "ID of the parent adventure entity (e.g., 'tlom.the-light-of-memoria')."}, "parent_section_id": {"type": ["string", "null"], "description": "ID of the parent adventure_section, if this is a sub-section."}, "parent_chapter_id": {"type": ["string", "null"], "description": "ID of the parent chapter, if this section belongs to a specific chapter."}, "order_in_adventure": {"type": ["integer", "string"], "description": "Sequence number or string indicating order within the adventure/parent section."}, "section_title": {"type": "string", "description": "Full human-readable title for this section (e.g., 'The Oubliette'). The 'name' field from universal_narrative_fields would be the slug (e.g., 'the-oubliette')."}, "goal_for_players_narrative": {"type": "string", "description": "Narrative description of what players are meant to achieve or discover."}, "gm_notes_plot": {"type": "string", "description": "GM notes on plot progression, key decisions, and consequences related to this section."}, "read_aloud_text": {"type": "string", "description": "Text to be read aloud to players when they enter or experience this section."}, "description_environment_narrative": {"type": "string", "description": "Narrative description of the environment: atmosphere, look, feel, non-mechanical features."}, "key_locations_narrative": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the key location within this section."}, "description": {"type": "string", "description": "Narrative description of this key location."}, "linked_location_id": {"type": ["string", "null"], "description": "ID of a dedicated 'location' entity if it exists (e.g., 'tlom.location.specific-room')."}}, "required": ["name", "description"]}}, "key_npcs_narrative": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the key NPC in this section."}, "role_description_narrative": {"type": "string", "description": "Narrative description of the NPC's role or initial presentation."}, "linked_npc_id": {"type": ["string", "null"], "description": "ID of a dedicated 'npc' entity if it exists (e.g., 'tlom.npc.elara')."}}, "required": ["name", "role_description_narrative"]}}, "key_items_narrative": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the key item in this section."}, "description_narrative": {"type": "string", "description": "Narrative description of the item and its relevance."}, "linked_item_id_placeholder": {"type": ["string", "null"], "description": "Placeholder for game-specific item ID if it's a unique mechanical item. Actual linking via suggested_mechanics_by_system."}}, "required": ["name", "description_narrative"]}}, "potential_challenges_narrative": {"type": "string", "description": "System-agnostic narrative description of challenges, obstacles, or conflicts."}, "suggested_mechanics_by_system": {"type": "object", "description": "Object where keys are game_system IDs (e.g., dnd5e, pf2e) and values are arrays of specific mechanical entities or descriptions relevant to this narrative section for that system.", "additionalProperties": {"type": "array", "items": {"type": "object", "properties": {"entity_id": {"type": "string", "description": "ID of a game-specific entity (e.g., monster.dnd5e.goblin, hazard.dnd5e.pit-trap)."}, "quantity": {"type": ["integer", "string"], "minimum": 1}, "placement_notes": {"type": "string", "description": "Notes on how or where this mechanical entity fits into the narrative scene."}, "role_or_purpose": {"type": "string", "description": "The narrative role of this mechanical element (e.g., 'guardian', 'obstacle', 'reward')."}}, "required": ["entity_id"]}}}, "treasure_narrative": {"type": "string", "description": "System-agnostic narrative description of rewards, treasure, or valuable information."}, "triggers_events_narrative": {"type": "array", "items": {"type": "object", "properties": {"trigger_description": {"type": "string", "description": "Narrative description of what triggers the event."}, "event_description_narrative": {"type": "string", "description": "Narrative outcome of the event."}, "linked_event_id": {"type": ["string", "null"], "description": "Optional ID to a more detailed event entity if one exists."}}, "required": ["trigger_description", "event_description_narrative"]}}, "foreshadowing_clues_narrative": {"type": "array", "items": {"type": "string"}}}, "required": ["parent_adventure_id", "section_title", "description_environment_narrative"]}, "location_narrative_yaml": {"allOf": [{"$ref": "#/definitions/universal_narrative_fields"}], "type": "object", "properties": {"entity_type": {"const": "location"}, "location_title": {"type": "string", "description": "Full human-readable title for this location. The 'name' field from universal_narrative_fields would be the slug."}, "parent_adventure_id": {"type": "string", "description": "ID of the parent adventure entity."}, "parent_section_id": {"type": ["string", "null"], "description": "ID of the adventure_section this location primarily belongs to, if applicable."}, "parent_chapter_id": {"type": ["string", "null"], "description": "ID of the parent chapter, if this location belongs to a specific chapter."}, "parent_location_id": {"type": ["string", "null"], "description": "ID of a parent location if this is a sub-location (e.g. a room within a building)."}, "location_type": {"type": "string", "enum": ["settlement", "building", "room", "area", "region", "dungeon", "wilderness", "structure", "other"], "description": "The type/category of this location"}, "location_scale": {"type": "string", "enum": ["tiny", "small", "medium", "large", "huge", "vast"], "description": "The relative size/scale of this location"}, "primary_chapter_id": {"type": ["string", "null"], "description": "ID of the chapter that primarily features this location, if applicable"}, "location_type_narrative": {"type": "string", "description": "Narrative type of location (e.g., 'ancient ruin', 'bustling tavern', 'eerie cave')."}, "description_general_narrative": {"type": "string", "description": "Overall narrative description of the location."}, "atmosphere_mood_narrative": {"type": "string", "description": "Description of the location's atmosphere, mood, sights, sounds, smells."}, "map_image_path_generic": {"type": ["string", "null"], "description": "Path to a generic or system-agnostic map image, if any."}, "points_of_interest_narrative": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the point of interest."}, "description_narrative": {"type": "string"}, "details_id": {"type": ["string", "null"], "description": "Link to a sub-location or detailed description entity."}}, "required": ["name", "description_narrative"]}}, "connections_narrative": {"type": "array", "items": {"type": "object", "properties": {"target_location_id": {"type": "string", "description": "ID of the connected location entity."}, "travel_method_narrative": {"type": "string", "description": "Narrative description of travel (e.g., 'hidden passage', 'cobbled road')."}, "travel_time_narrative": {"type": "string", "description": "Narrative sense of travel time (e.g., 'a few minutes walk', 'a short ride')."}, "description_narrative": {"type": "string", "description": "Further narrative details about the connection."}}, "required": ["target_location_id"]}}, "suggested_mechanics_by_system": {"$ref": "#/definitions/adventure_section_yaml/properties/suggested_mechanics_by_system"}, "events_triggers_narrative": {"$ref": "#/definitions/adventure_section_yaml/properties/triggers_events_narrative"}}, "required": ["location_title", "parent_adventure_id", "description_general_narrative"]}, "npc_narrative_yaml": {"allOf": [{"$ref": "#/definitions/universal_narrative_fields"}], "type": "object", "properties": {"entity_type": {"const": "npc"}, "npc_full_name_or_title": {"type": "string", "description": "Full human-readable name or title of the NPC. The 'name' field from universal_narrative_fields would be the slug."}, "parent_adventure_id": {"type": "string", "description": "ID of the parent adventure entity."}, "role_in_adventure_narrative": {"type": "string", "description": "Narrative description of the NPC's role in the adventure."}, "description_physical_narrative": {"type": "string"}, "description_personality_narrative": {"type": "string"}, "motivations_goals_narrative": {"type": "array", "items": {"type": "string"}}, "ideals_narrative": {"type": "array", "items": {"type": "string"}}, "bonds_narrative": {"type": "array", "items": {"type": "string"}}, "flaws_secrets_narrative": {"type": "array", "items": {"type": "string"}}, "relationships_narrative": {"type": "array", "items": {"type": "object", "properties": {"target_npc_id": {"type": "string", "description": "ID of another NPC entity."}, "relationship_type_narrative": {"type": "string", "description": "e.g., '<PERSON>', 'Rival', '<PERSON>', '<PERSON><PERSON>'."}, "details_narrative": {"type": "string", "description": "Narrative details of the relationship."}}, "required": ["target_npc_id", "relationship_type_narrative"]}}, "current_location_id_narrative": {"type": ["string", "null"], "description": "Narrative primary location (ID of a location entity)."}, "key_inventory_items_narrative": {"type": "array", "items": {"type": "string"}, "description": "Narratively important items the NPC possesses. Mechanical items linked via suggested_mechanics_by_system."}, "faction_affiliation_narrative": {"type": "array", "items": {"type": "object", "properties": {"faction_name_narrative": {"type": "string"}, "rank_narrative": {"type": "string"}, "details_narrative": {"type": "string"}}, "required": ["faction_name_narrative"]}}, "dialogue_cues_voice_mannerisms": {"type": "array", "items": {"type": "string"}}, "suggested_mechanics_by_system": {"$ref": "#/definitions/adventure_section_yaml/properties/suggested_mechanics_by_system"}}, "required": ["npc_full_name_or_title", "parent_adventure_id", "role_in_adventure_narrative"]}, "campaign_yaml": {"allOf": [{"$ref": "#/definitions/universal_narrative_fields"}], "type": "object", "properties": {"entity_type": {"const": "campaign"}, "campaign_title": {"type": "string", "description": "Full human-readable title of the campaign (e.g., 'Curse of Strahd Campaign')"}, "campaign_theme": {"type": "string", "description": "Primary theme or genre of the campaign (e.g., 'Gothic Horror', 'High Fantasy', 'Political Intrigue')"}, "target_level_range": {"type": "object", "properties": {"start_level": {"type": "integer"}, "end_level": {"type": "integer"}}, "description": "Expected character level progression for the campaign"}, "estimated_duration_sessions": {"type": ["integer", "null"], "description": "Estimated number of game sessions for the full campaign"}, "contained_adventures": {"type": "array", "items": {"type": "string"}, "description": "IDs of adventures that are part of this campaign"}, "major_npcs": {"type": "array", "items": {"type": "string"}, "description": "IDs of major NPCs that appear throughout the campaign"}, "central_locations": {"type": "array", "items": {"type": "string"}, "description": "IDs of key locations that are central to the campaign"}, "overarching_plot": {"type": "string", "description": "High-level description of the campaign's main storyline"}}, "required": ["campaign_title", "campaign_theme", "overarching_plot"]}, "chapter_yaml": {"allOf": [{"$ref": "#/definitions/universal_narrative_fields"}], "type": "object", "properties": {"entity_type": {"const": "chapter"}, "chapter_title": {"type": "string", "description": "Full human-readable title for this chapter"}, "parent_adventure_id": {"type": "string", "description": "ID of the parent adventure entity"}, "chapter_number": {"type": ["integer", "string"], "description": "Chapter number or identifier within the adventure"}, "chapter_objectives": {"type": "array", "items": {"type": "string"}, "description": "Primary objectives or goals for this chapter"}, "contained_sections": {"type": "array", "items": {"type": "string"}, "description": "IDs of adventure sections contained in this chapter"}, "key_events": {"type": "array", "items": {"type": "string"}, "description": "IDs of major events that occur in this chapter"}, "chapter_summary": {"type": "string", "description": "Overview of what happens in this chapter"}, "primary_location_id": {"type": ["string", "null"], "description": "ID of the main location featured in this chapter, if applicable"}}, "required": ["chapter_title", "parent_adventure_id", "chapter_summary"]}, "faction_yaml": {"allOf": [{"$ref": "#/definitions/universal_narrative_fields"}], "type": "object", "properties": {"entity_type": {"const": "faction"}, "faction_name": {"type": "string", "description": "Full name of the faction or organization"}, "faction_type": {"type": "string", "enum": ["political", "religious", "criminal", "mercantile", "military", "scholarly", "secret", "organization", "guild", "cult", "order", "other"], "description": "Primary type or nature of the faction/organization"}, "power_level": {"type": "string", "enum": ["local", "regional", "national", "international", "planar"], "description": "Scope of the faction's influence and power"}, "primary_goals": {"type": "array", "items": {"type": "string"}, "description": "Main objectives and goals of the faction"}, "key_members": {"type": "array", "items": {"type": "object", "properties": {"npc_id": {"type": "string"}, "role": {"type": "string"}, "importance": {"type": "string", "enum": ["leader", "lieutenant", "member", "ally", "contact"]}}}}, "allied_factions": {"type": "array", "items": {"type": "string"}, "description": "IDs of factions that are allies"}, "enemy_factions": {"type": "array", "items": {"type": "string"}, "description": "IDs of factions that are enemies"}, "resources_and_assets": {"type": "array", "items": {"type": "string"}, "description": "Description of the faction's resources, wealth, and assets"}, "territory_controlled": {"type": "array", "items": {"type": "string"}, "description": "IDs of locations or regions controlled by this faction"}}, "required": ["faction_name", "faction_type", "power_level", "primary_goals"]}, "event_yaml": {"allOf": [{"$ref": "#/definitions/universal_narrative_fields"}], "type": "object", "properties": {"entity_type": {"const": "event"}, "event_title": {"type": "string", "description": "Full human-readable title for this event"}, "parent_adventure_id": {"type": ["string", "null"], "description": "ID of the parent adventure entity, if applicable"}, "parent_chapter_id": {"type": ["string", "null"], "description": "ID of the parent chapter, if this event belongs to a specific chapter"}, "event_type": {"type": "string", "enum": ["encounter", "social", "exploration", "revelation", "climax", "transition", "other"], "description": "Type of event this represents"}, "trigger_conditions": {"type": "array", "items": {"type": "string"}, "description": "Conditions that trigger this event"}, "event_description": {"type": "string", "description": "Detailed description of what happens during this event"}, "participants": {"type": "array", "items": {"type": "string"}, "description": "IDs of NPCs, factions, or other entities involved in this event"}, "location_id": {"type": ["string", "null"], "description": "ID of the location where this event takes place"}, "consequences": {"type": "array", "items": {"type": "string"}, "description": "Potential outcomes or consequences of this event"}, "follow_up_events": {"type": "array", "items": {"type": "string"}, "description": "IDs of events that may follow this one"}}, "required": ["event_title", "event_type", "event_description"]}, "quest_yaml": {"allOf": [{"$ref": "#/definitions/universal_narrative_fields"}], "type": "object", "properties": {"entity_type": {"const": "quest"}, "quest_title": {"type": "string", "description": "Full human-readable title for this quest"}, "parent_adventure_id": {"type": ["string", "null"], "description": "ID of the parent adventure entity, if applicable"}, "quest_giver_id": {"type": ["string", "null"], "description": "ID of the NPC or entity that gives this quest"}, "quest_type": {"type": "string", "enum": ["main", "side", "personal", "faction", "exploration", "fetch", "escort", "elimination", "other"], "description": "Type of quest this represents"}, "objectives": {"type": "array", "items": {"type": "object", "properties": {"description": {"type": "string"}, "completed": {"type": "boolean", "default": false}, "optional": {"type": "boolean", "default": false}}}, "description": "List of quest objectives"}, "rewards": {"type": "array", "items": {"type": "string"}, "description": "Rewards for completing this quest"}, "prerequisites": {"type": "array", "items": {"type": "string"}, "description": "IDs of quests or events that must be completed first"}, "related_locations": {"type": "array", "items": {"type": "string"}, "description": "IDs of locations relevant to this quest"}, "related_npcs": {"type": "array", "items": {"type": "string"}, "description": "IDs of NPCs relevant to this quest"}}, "required": ["quest_title", "quest_type", "objectives"]}}}