# Simplified Schema Examples

## Overview

This document demonstrates how the simplified schema design handles various entity types through type fields rather than separate entity categories.

## Location Examples

### Settlement (Village)
```yaml
---
id: "cos.location.village-of-barovia"
entity_type: "location"
location_type: "settlement"
location_scale: "large"
# ... other location fields
---
```

### Building
```yaml
---
id: "cos.location.blood-on-vine-tavern"
entity_type: "location"
location_type: "building"
location_scale: "medium"
parent_location_id: "cos.location.village-of-barovia"
# ... other location fields
---
```

### Room
```yaml
---
id: "cos.location.tavern-common-room"
entity_type: "location"
location_type: "room"
location_scale: "medium"
parent_location_id: "cos.location.blood-on-vine-tavern"
# ... other location fields
---
```

### Region
```yaml
---
id: "cos.location.barovia-valley"
entity_type: "location"
location_type: "region"
location_scale: "vast"
# ... other location fields
---
```

## Faction Examples

### Political Organization
```yaml
---
id: "cos.faction.barovian-government"
entity_type: "faction"
faction_type: "political"
faction_name: "Barovian Government"
# ... other faction fields
---
```

### Religious Organization
```yaml
---
id: "cos.faction.church-of-st-andral"
entity_type: "faction"
faction_type: "religious"
faction_name: "Church of St. Andral"
# ... other faction fields
---
```

### Formal Organization
```yaml
---
id: "cos.faction.order-of-the-silver-dragon"
entity_type: "faction"
faction_type: "organization"
faction_name: "Order of the Silver Dragon"
# ... other faction fields
---
```

### Guild
```yaml
---
id: "waterdeep.faction.harpers"
entity_type: "faction"
faction_type: "guild"
faction_name: "The Harpers"
# ... other faction fields
---
```

## D&D 5e Item Examples

### Regular Item
```yaml
---
id: "longsword.dnd5e"
entity_type: "item"
item_type_dnd5e: "Weapon (longsword)"
# ... other item fields
---
```

### Vehicle
```yaml
---
id: "sailing-ship.dnd5e"
entity_type: "item"
item_type_dnd5e: "Vehicle (ship)"
# ... other item fields
---
```

### Mount
```yaml
---
id: "warhorse.dnd5e"
entity_type: "item"
item_type_dnd5e: "Mount (horse)"
# ... other item fields
---
```

## D&D 5e Condition Examples

### Standard Condition
```yaml
---
id: "frightened.dnd5e"
entity_type: "condition"
condition_type: "standard"
duration_type: "special"
mechanical_effects:
  - "Disadvantage on ability checks and attack rolls while source of fear is within line of sight"
  - "Cannot willingly move closer to source of fear"
# ... other condition fields
---
```

### Disease
```yaml
---
id: "cackle-fever.dnd5e"
entity_type: "condition"
condition_type: "disease"
duration_type: "until_cured"
onset_time: "1d4 hours"
contagious: true
mechanical_effects:
  - "Wisdom saving throw DC 13 or gain one level of madness"
symptoms:
  - "Uncontrollable laughter"
  - "Fever and chills"
removal_methods:
  - "Lesser restoration spell"
  - "Natural recovery after 7 days"
# ... other condition fields
---
```

### Madness
```yaml
---
id: "short-term-madness-paranoia.dnd5e"
entity_type: "condition"
condition_type: "madness"
duration_type: "10_minutes"
mechanical_effects:
  - "The character becomes paranoid and suspicious of everyone"
  - "Disadvantage on Wisdom (Insight) checks"
symptoms:
  - "Constantly looking over shoulder"
  - "Whispering about conspiracies"
removal_methods:
  - "Calm emotions spell"
  - "Time (duration expires)"
# ... other condition fields
---
```

### Curse
```yaml
---
id: "lycanthropy-werewolf.dnd5e"
entity_type: "condition"
condition_type: "curse"
duration_type: "permanent"
mechanical_effects:
  - "Shapechanger trait"
  - "Damage immunities to bludgeoning, piercing, and slashing from nonmagical attacks not made with silvered weapons"
  - "Alignment shifts toward chaotic evil"
removal_methods:
  - "Remove curse spell"
  - "Wish spell"
# ... other condition fields
---
```

## Nested Location Hierarchy Example

```yaml
# Region
---
id: "cos.location.barovia-valley"
entity_type: "location"
location_type: "region"
location_scale: "vast"
relationships:
  contains: ["cos.location.village-of-barovia", "cos.location.castle-ravenloft"]
---

# Settlement within Region
---
id: "cos.location.village-of-barovia"
entity_type: "location"
location_type: "settlement"
location_scale: "large"
parent_location_id: "cos.location.barovia-valley"
relationships:
  contained_by: "cos.location.barovia-valley"
  contains: ["cos.location.blood-on-vine-tavern", "cos.location.church-of-st-andral"]
---

# Building within Settlement
---
id: "cos.location.blood-on-vine-tavern"
entity_type: "location"
location_type: "building"
location_scale: "medium"
parent_location_id: "cos.location.village-of-barovia"
relationships:
  contained_by: "cos.location.village-of-barovia"
  contains: ["cos.location.tavern-common-room", "cos.location.tavern-cellar"]
---

# Room within Building
---
id: "cos.location.tavern-common-room"
entity_type: "location"
location_type: "room"
location_scale: "medium"
parent_location_id: "cos.location.blood-on-vine-tavern"
relationships:
  contained_by: "cos.location.blood-on-vine-tavern"
  contains: ["cos.npc.tavern-patrons"]
---
```

## File Organization Structure

```
content/
├── campaigns/
│   └── curse-of-strahd-campaign.md
├── adventures/
│   └── cos.village-of-barovia.md
├── chapters/
│   └── cos.chapter-2-village-of-barovia.md
├── adventure-sections/
│   └── cos.section.tavern-investigation.md
├── locations/                    # All locations regardless of type
│   ├── cos.location.barovia-valley.md          # region
│   ├── cos.location.village-of-barovia.md      # settlement
│   ├── cos.location.blood-on-vine-tavern.md    # building
│   └── cos.location.tavern-common-room.md      # room
├── npcs/
│   └── cos.npc.ireena-kolyana.md
├── factions/                     # All factions/organizations
│   ├── cos.faction.barovian-government.md      # political
│   ├── cos.faction.church-of-st-andral.md      # religious
│   └── cos.faction.order-of-silver-dragon.md   # organization
├── events/
│   └── cos.event.meeting-ireena.md
├── quests/
│   └── cos.quest.find-ireena.md
├── encounters/                   # D&D 5e encounters
│   └── cos.encounter.tavern-brawl.md
├── items/                        # All items including vehicles
│   ├── longsword.dnd5e.md                      # weapon
│   └── sailing-ship.dnd5e.md                   # vehicle
└── conditions/                   # All conditions including diseases
    ├── frightened.dnd5e.md                     # standard
    ├── cackle-fever.dnd5e.md                   # disease
    └── lycanthropy-werewolf.dnd5e.md           # curse
```

## Benefits of This Approach

### 1. **Simplified Processing**
- Single entity type per directory
- Consistent processing logic
- Type differentiation through fields

### 2. **Easier Reassembly**
- Clear hierarchical relationships
- Fewer entity types to coordinate
- Consistent relationship patterns

### 3. **Maintained Functionality**
- All original distinctions preserved
- Rich type information available
- Flexible categorization

### 4. **Pipeline Efficiency**
- Reduced complexity in extraction
- Simpler validation logic
- Cleaner file organization

This simplified approach provides all the organizational benefits while maintaining the efficiency needed for your PDF-to-Markdown pipeline.
