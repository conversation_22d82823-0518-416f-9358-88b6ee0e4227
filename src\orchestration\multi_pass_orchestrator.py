import logging
from typing import Dict, List, Optional

from src.processing.document_preprocessor_service import DocumentPreProcessorService
from src.processing.markdown_processor_service import MarkdownProcessorService
from src.models import DocumentAnalysisResult

logger = logging.getLogger(__name__)

class MultiPassOrchestrator:
    """
    Orchestrates the multi-pass LLM processing pipeline for TTRPG documents.
    """

    def __init__(self, narrative_schema_path: str, dnd5e_schema_path: str, output_root: str):
        """
        Initializes the MultiPassOrchestrator.

        Args:
            narrative_schema_path: Path to the narrative TTRPG content JSON schema.
            dnd5e_schema_path: Path to the D&D 5e game-specific JSON schema.
            output_root: Root directory for structured output files.
        """
        self.pass1_service = DocumentPreProcessorService(narrative_schema_path=narrative_schema_path)
        self.pass2_service = MarkdownProcessorService(
            narrative_schema_path=narrative_schema_path,
            dnd5e_schema_path=dnd5e_schema_path,
            output_root=output_root
        )
        logger.info("MultiPassOrchestrator initialized.")

    def process_document(self, raw_md_content: str, source_document_name: str, initial_game_system: str) -> List[str]:
        """
        Processes a raw Markdown document through the multi-pass pipeline.

        Args:
            raw_md_content: The full Markdown content as a string.
            source_document_name: The name of the source document.
            initial_game_system: An initial hint for the game system.

        Returns:
            A list of file paths to the created structured Markdown files.
        """
        logger.info(f"Starting multi-pass processing for document: {source_document_name}")

        # --- Pass 1: Document Analysis and Pre-processing ---
        logger.info("Executing Pass 1: Document Analysis...")
        pass1_result: DocumentAnalysisResult = self.pass1_service.analyze_document(
            raw_md_content=raw_md_content,
            source_document_name=source_document_name,
            initial_game_system=initial_game_system
        )
        logger.info(f"Pass 1 completed. Requires chunking: {pass1_result.requires_chunking}, Chunks: {len(pass1_result.content_chunks)}")
        logger.debug(f"Pass 1 overall_metadata: {pass1_result.overall_metadata}")

        all_created_files: List[str] = []

        # --- Pass 2: Entity Extraction per Chunk ---
        logger.info("Executing Pass 2: Entity Extraction for each chunk...")
        for i, chunk_content in enumerate(pass1_result.content_chunks):
            logger.info(f"Processing chunk {i+1}/{len(pass1_result.content_chunks)} for '{source_document_name}'")
            try:
                chunk_created_files = self.pass2_service.process_markdown(
                    content_chunk=chunk_content,
                    overall_metadata=pass1_result.overall_metadata,
                    source_document_name=pass1_result.source_document_name, # Use name from pass1_result
                    game_system=pass1_result.identified_game_system # Use game_system from pass1_result
                )
                all_created_files.extend(chunk_created_files)
                logger.info(f"Chunk {i+1} processing completed. {len(chunk_created_files)} files created for this chunk.")
            except Exception as e:
                logger.error(f"Error processing chunk {i+1} for '{source_document_name}': {e}", exc_info=True)
                # Optionally, decide if you want to stop processing or continue with other chunks

        logger.info(f"Multi-pass processing completed for {source_document_name}. Total files created: {len(all_created_files)}")
        return all_created_files

if __name__ == '__main__':
    # Example Usage (for testing this orchestrator in isolation)
    # This would typically be run from a main script like run_llm_processor_test.py
    import os
    from dotenv import load_dotenv
    load_dotenv() # Ensure .env is loaded if OPENAI_API_KEY is used by LLM services

    # Define paths (adjust as necessary if running this file directly)
    # Assuming this script is in src/orchestration, config is ../../config
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    narrative_schema_path_main = os.path.join(project_root, "config", "narrative_entities_schema.json")
    dnd5e_schema_path_main = os.path.join(project_root, "config", "dnd5e_entities_schema.json")
    output_root_main = os.path.join(project_root, "output", "multi_pass_test_output")
    
    # Create dummy schemas if they don't exist for this isolated test
    os.makedirs(os.path.dirname(narrative_schema_path_main), exist_ok=True)
    if not os.path.exists(narrative_schema_path_main):
        with open(narrative_schema_path_main, "w") as f:
            json.dump({"title": "Mock Narrative Schema", "definitions": {"adventure_yaml": {"type": "object"}}}, f)
    if not os.path.exists(dnd5e_schema_path_main):
        with open(dnd5e_schema_path_main, "w") as f:
            json.dump({"title": "Mock D&D 5e Schema", "definitions": {"monster_dnd5e_yaml": {"type": "object"}}}, f)


    orchestrator = MultiPassOrchestrator(
        narrative_schema_path=narrative_schema_path_main,
        dnd5e_schema_path=dnd5e_schema_path_main,
        output_root=output_root_main
    )

    sample_md_content = """# Test Adventure: The Goblin's Gauntlet
This is a test adventure for the multi-pass system.
It involves goblins and a gauntlet.

## Chapter 1: The Approach
The heroes approach a menacing cave.
A **Goblin Sentry** (Monster, D&D 5e) is on watch.
It has AC 13, HP 7. Uses a Scimitar.

## Chapter 2: Inside the Cave
The cave is dark.
They find a *Potion of Healing* (Item, D&D 5e).
**Gribnock the Goblin Boss** (NPC, Narrative) confronts them. He is a cunning goblin.
"""

    print("--- Running MultiPassOrchestrator Example ---")
    created_files_example = orchestrator.process_document(
        raw_md_content=sample_md_content,
        source_document_name="goblin_gauntlet_test",
        initial_game_system="dnd5e"
    )

    print("\nCreated files:")
    for f_path in created_files_example:
        print(f"- {f_path}")
    print(f"\nOutput files should be in: {os.path.abspath(output_root_main)}")
