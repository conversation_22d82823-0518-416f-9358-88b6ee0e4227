{"$schema": "http://json-schema.org/draft-07/schema#", "title": "D&D 5e Game-Specific Content Schema", "description": "Defines the structure for D&D 5e specific mechanical YAML frontmatter in TTRPG Markdown files.", "type": "object", "definitions": {"universal_dnd5e_fields": {"type": "object", "properties": {"id": {"type": "string", "description": "Globally unique identifier, typically constructed as: [entity_type].dnd5e.[entity_name] (e.g., monster.dnd5e.goblin)"}, "entity_type": {"type": "string", "enum": ["monster", "item", "spell", "hazard", "condition", "class", "race", "background", "feat_dnd5e", "encounter_dnd5e", "magic_item"], "description": "The type of D&D 5e entity."}, "name": {"type": "string", "description": "Human-readable name of the entity, often used as the last part of the filename and ID (e.g., 'goblin', 'potion-of-healing'). Should be slugified for IDs/filenames."}, "game_system": {"type": "string", "const": "dnd5e", "description": "Indicates that this entity is for the D&D 5e game system."}, "source_book": {"type": "string", "description": "Official D&D 5e sourcebook (e.g., 'Monster Manual', 'Player's Handbook')."}, "source_adventure_id": {"type": ["string", "null"], "description": "Optional ID of the narrative adventure entity if this D&D 5e entity is uniquely defined in that adventure (e.g., 'tlom.the-light-of-memoria')."}, "organizational_hierarchy": {"type": "object", "description": "Defines the hierarchical position of this entity within the larger organizational structure", "properties": {"campaign_id": {"type": ["string", "null"], "description": "ID of the parent campaign this entity belongs to"}, "module_id": {"type": ["string", "null"], "description": "ID of the parent module/adventure this entity belongs to"}, "chapter_id": {"type": ["string", "null"], "description": "ID of the chapter within the module"}, "section_id": {"type": ["string", "null"], "description": "ID of the section within the chapter"}, "subsection_id": {"type": ["string", "null"], "description": "ID of the subsection for fine-grained organization"}}}, "relationships": {"type": "object", "description": "Defines relationships between this entity and other entities in the system", "properties": {"contains": {"type": "array", "items": {"type": "string"}, "description": "IDs of entities that this entity contains"}, "contained_by": {"type": ["string", "null", "array"], "items": {"type": "string"}, "description": "ID(s) of parent entities that contain this entity. Can be a single string ID, null, or an array of string IDs for entities with multiple parents."}, "references": {"type": "array", "items": {"type": "string"}, "description": "IDs of entities that this entity references or mentions"}, "referenced_by": {"type": "array", "items": {"type": "string"}, "description": "IDs of entities that reference this entity"}, "depends_on": {"type": "array", "items": {"type": "string"}, "description": "IDs of entities that this entity depends on for context or completion"}, "enables": {"type": "array", "items": {"type": "string"}, "description": "IDs of entities that this entity enables or unlocks"}}}, "content_organization": {"type": "object", "description": "Metadata for organizing and presenting content", "properties": {"display_order": {"type": ["integer", "null"], "description": "Numeric order for displaying this entity among siblings"}, "content_type": {"type": "string", "enum": ["mechanical", "reference", "encounter", "treasure", "challenge"], "description": "The primary type of content this entity represents"}, "complexity_level": {"type": "string", "enum": ["simple", "moderate", "complex", "advanced"], "description": "Indicates the complexity level for GM preparation"}, "encounter_difficulty": {"type": ["string", "null"], "enum": [null, "trivial", "easy", "medium", "hard", "deadly"], "description": "D&D 5e encounter difficulty rating, if applicable"}, "prerequisites": {"type": "array", "items": {"type": "string"}, "description": "IDs of entities that should be completed or understood before this one"}}}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Keywords for searching and categorization specific to D&D 5e mechanics."}, "summary_mechanical": {"type": "string", "description": "A brief one or two-sentence description of the entity's mechanical role or key features."}}, "required": ["id", "entity_type", "name", "game_system", "source_book"]}, "monster_dnd5e_yaml": {"allOf": [{"$ref": "#/definitions/universal_dnd5e_fields"}], "type": "object", "properties": {"entity_type": {"const": "monster"}, "size": {"type": "string", "enum": ["Tiny", "Small", "Medium", "Large", "<PERSON>ge", "Gargantuan"]}, "monster_type": {"type": "string", "description": "e.g., humanoid, beast, fiend, undead."}, "subtype": {"type": ["string", "null"], "description": "e.g., (goblinoid), (elf), (demon)."}, "alignment": {"type": "string", "description": "e.g., chaotic evil, lawful good, unaligned."}, "ac": {"type": "integer", "description": "Armor Class."}, "ac_description": {"type": ["string", "null"], "description": "Description of armor if not standard (e.g., 'natural armor', 'plate armor')."}, "hp": {"type": "integer", "description": "Hit Points."}, "hp_formula": {"type": ["string", "null"], "description": "Hit Dice formula (e.g., '4d8 + 8')."}, "speed": {"type": "object", "properties": {"walk": {"type": "string"}, "fly": {"type": "string"}, "swim": {"type": "string"}, "climb": {"type": "string"}, "burrow": {"type": "string"}}, "additionalProperties": false}, "ability_scores": {"type": "object", "properties": {"str": {"type": "integer"}, "dex": {"type": "integer"}, "con": {"type": "integer"}, "int": {"type": "integer"}, "wis": {"type": "integer"}, "cha": {"type": "integer"}}, "required": ["str", "dex", "con", "int", "wis", "cha"]}, "saving_throws": {"type": "array", "items": {"type": "object", "properties": {"ability": {"type": "string", "enum": ["STR", "DEX", "CON", "INT", "WIS", "CHA"]}, "bonus": {"type": "integer"}}, "required": ["ability", "bonus"]}}, "skills_proficiency": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Skill name (e.g., '<PERSON><PERSON><PERSON>', 'Perception')."}, "bonus": {"type": "integer"}}, "required": ["name", "bonus"]}}, "damage_vulnerabilities": {"type": "array", "items": {"type": "string"}}, "damage_resistances": {"type": "array", "items": {"type": "string"}}, "damage_immunities": {"type": "array", "items": {"type": "string"}}, "condition_immunities": {"type": "array", "items": {"type": "string"}}, "senses": {"type": "object", "properties": {"blindsight": {"type": "string"}, "darkvision": {"type": "string"}, "tremorsense": {"type": "string"}, "truesight": {"type": "string"}, "passive_perception": {"type": "integer"}}, "additionalProperties": true}, "languages": {"type": "array", "items": {"type": "string"}}, "challenge_rating": {"type": "string", "description": "Challenge Rating (CR), e.g., '1/4', '5'."}, "xp": {"type": "integer", "description": "Experience points awarded for defeating."}, "special_abilities": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string", "enum": ["trait", "action_option", "spellcasting", "aura"], "default": "trait"}}, "required": ["name", "description"]}}, "actions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type_description": {"type": "string", "description": "e.g., 'Melee Weapon Attack', 'Ranged Spell Attack'."}, "attack_bonus": {"type": ["integer", "null"]}, "reach": {"type": ["string", "null"]}, "range": {"type": ["string", "null"]}, "target": {"type": ["string", "null"]}, "damage": {"type": "array", "items": {"type": "object", "properties": {"dice_formula": {"type": "string"}, "damage_type": {"type": "string"}}, "required": ["dice_formula", "damage_type"]}}, "description": {"type": "string", "description": "Full description of the action, including effects on hit or miss."}}, "required": ["name", "description"]}}, "reactions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}}, "required": ["name", "description"]}}, "legendary_actions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "cost": {"type": "integer", "default": 1, "description": "Number of legendary actions this costs."}}, "required": ["name", "description"]}}, "lair_actions_description": {"type": ["string", "null"], "description": "General description of when lair actions can be taken."}, "lair_actions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}}, "required": ["name", "description"]}}, "regional_effects_description": {"type": ["string", "null"], "description": "General description of regional effects."}, "regional_effects": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}}, "required": ["name", "description"]}}}, "required": ["size", "monster_type", "alignment", "ac", "hp", "speed", "ability_scores", "challenge_rating", "xp"]}, "item_dnd5e_yaml": {"allOf": [{"$ref": "#/definitions/universal_dnd5e_fields"}], "type": "object", "properties": {"entity_type": {"const": "item"}, "item_type_dnd5e": {"type": "string", "description": "e.g., 'Weapon (longsword)', 'Armor (plate)', 'Potion', 'Wondrous item', 'Vehicle (ship)', 'Mount (horse)'."}, "rarity_dnd5e": {"type": "string", "enum": ["Common", "Uncommon", "Rare", "Very Rare", "Legendary", "Artifact"]}, "value_gp": {"type": ["number", "string", "null"], "description": "Value in gold pieces, or descriptive text like 'Priceless'."}, "weight_lb": {"type": ["number", "string", "null"], "description": "Weight in pounds."}, "properties_mechanical": {"type": "array", "items": {"type": "string"}, "description": "Mechanical properties, e.g., for weapons: 'Finesse', 'Versatile (1d10)'."}, "requires_attunement": {"type": ["boolean", "string"], "description": "True if attunement is required, or a string describing special attunement conditions."}, "attunement_by": {"type": ["string", "null"], "description": "Class or type of character that can attune (e.g., 'by a spellcaster')."}, "description_mechanical": {"type": "string", "description": "Full mechanical description of the item's effects and usage."}, "armor_class_base": {"type": ["integer", "null"], "description": "Base AC for armor."}, "armor_class_dex_bonus": {"type": ["boolean", "string", "null"], "description": "True if DEX modifier applies, 'Max 2' if capped, or null."}, "strength_requirement": {"type": ["integer", "null"], "description": "Minimum STR score to use without penalty."}, "stealth_disadvantage": {"type": ["boolean", "null"], "description": "True if armor imposes disadvantage on Stealth checks."}, "damage_dice": {"type": ["string", "null"], "description": "Damage dice for weapons (e.g., '1d8')."}, "damage_type": {"type": ["string", "null"], "description": "Damage type for weapons (e.g., 'slashing', 'piercing')."}, "charges": {"type": ["object", "null"], "properties": {"max": {"type": "integer"}, "current": {"type": "integer"}, "recharge_rule": {"type": "string", "description": "e.g., '1d6+1 daily at dawn'"}}}}, "required": ["item_type_dnd5e", "rarity_dnd5e", "description_mechanical"]}, "spell_dnd5e_yaml": {"allOf": [{"$ref": "#/definitions/universal_dnd5e_fields"}], "type": "object", "properties": {"entity_type": {"const": "spell"}, "spell_level": {"type": "integer", "minimum": 0, "maximum": 9, "description": "0 for cantrips."}, "school_of_magic": {"type": "string", "enum": ["Abjuration", "Conjuration", "Divination", "Enchantment", "Evocation", "Illusion", "Necromancy", "Transmutation"]}, "casting_time": {"type": "string"}, "range": {"type": "string"}, "components": {"type": "object", "properties": {"verbal": {"type": "boolean", "default": false}, "somatic": {"type": "boolean", "default": false}, "material": {"type": "boolean", "default": false}, "material_description": {"type": ["string", "null"], "description": "Description of material components, including cost if any."}}}, "duration": {"type": "string", "description": "e.g., 'Instantaneous', '1 minute', 'Concentration, up to 10 minutes'."}, "concentration": {"type": "boolean", "default": false, "description": "True if the spell requires concentration."}, "ritual": {"type": "boolean", "default": false, "description": "True if the spell can be cast as a ritual."}, "area_of_effect_description": {"type": ["string", "null"], "description": "e.g., '30-foot cone', 'Self (10-foot radius sphere)'."}, "saving_throw_ability": {"type": ["string", "null"], "enum": [null, "Strength", "Dexterity", "Constitution", "Intelligence", "Wisdom", "Charisma"], "description": "Ability for saving throw, or null if none."}, "effect_on_save": {"type": ["string", "null"], "description": "e.g., 'half damage', 'negates effect'."}, "damage_effect": {"type": "array", "items": {"type": "object", "properties": {"dice_formula": {"type": "string"}, "damage_type": {"type": "string"}, "notes": {"type": ["string", "null"], "description": "e.g., 'per missile', 'on a failed save'"}}, "required": ["dice_formula", "damage_type"]}}, "conditions_applied": {"type": "array", "items": {"type": "string"}}, "healing_amount": {"type": ["string", "null"], "description": "Healing dice formula or amount."}, "description_full": {"type": "string", "description": "Complete description of the spell's effects."}, "at_higher_levels": {"type": ["string", "null"], "description": "Description of effects when cast using a higher-level spell slot."}, "classes_can_learn": {"type": "array", "items": {"type": "string"}, "description": "List of classes that can learn/prepare this spell."}}, "required": ["spell_level", "school_of_magic", "casting_time", "range", "components", "duration", "description_full"]}, "hazard_dnd5e_yaml": {"allOf": [{"$ref": "#/definitions/universal_dnd5e_fields"}], "type": "object", "properties": {"entity_type": {"const": "hazard"}, "hazard_type": {"type": "string", "enum": ["Mechanical Trap", "Magical Trap", "Environmental Hazard", "Complex Trap"], "description": "Type of hazard."}, "trigger_description": {"type": "string", "description": "How the hazard is triggered."}, "effect_description": {"type": "string", "description": "The effect of the hazard when triggered."}, "detection_dc": {"type": ["object", "null"], "properties": {"skill": {"type": "string", "default": "Wisdom (Perception)"}, "dc": {"type": "integer"}}, "description": "Skill and DC to detect the hazard."}, "disarm_dc": {"type": ["object", "null"], "properties": {"skill": {"type": "string", "default": "Dexterity (Thieves' Tools)"}, "dc": {"type": "integer"}, "notes": {"type": ["string", "null"], "description": "Special notes on disarming."}}, "description": "Skill and DC to disable/disarm the hazard."}, "attack_bonus": {"type": ["integer", "null"], "description": "Attack bonus if the hazard makes an attack roll."}, "damage_effects": {"$ref": "#/definitions/spell_dnd5e_yaml/properties/damage_effect"}, "saving_throw_dc": {"type": ["object", "null"], "properties": {"ability": {"type": "string", "enum": ["Strength", "Dexterity", "Constitution", "Intelligence", "Wisdom", "Charisma"]}, "dc": {"type": "integer"}}, "description": "Saving throw ability and DC to resist/mitigate effects."}, "reset_condition": {"type": ["string", "null"], "description": "How or if the trap resets (e.g., 'Automatic reset', 'Manual reset required')."}, "countermeasures": {"type": ["string", "null"], "description": "Other ways to bypass or deal with the hazard."}}, "required": ["hazard_type", "trigger_description", "effect_description"]}, "encounter_dnd5e_yaml": {"allOf": [{"$ref": "#/definitions/universal_dnd5e_fields"}], "type": "object", "properties": {"entity_type": {"const": "encounter_dnd5e"}, "encounter_title": {"type": "string", "description": "Full human-readable title for this encounter"}, "encounter_type": {"type": "string", "enum": ["combat", "social", "exploration", "puzzle", "trap", "mixed"], "description": "Primary type of encounter"}, "difficulty_rating": {"type": "string", "enum": ["trivial", "easy", "medium", "hard", "deadly"], "description": "D&D 5e encounter difficulty rating"}, "party_level": {"type": "integer", "minimum": 1, "maximum": 20, "description": "Expected party level for this encounter"}, "party_size": {"type": "integer", "minimum": 1, "maximum": 8, "default": 4, "description": "Expected party size for this encounter"}, "creatures": {"type": "array", "items": {"type": "object", "properties": {"monster_id": {"type": "string", "description": "ID of the monster entity"}, "quantity": {"type": "integer", "minimum": 1}, "role": {"type": "string", "enum": ["minion", "standard", "elite", "boss", "ally"]}, "initiative_modifier": {"type": ["integer", "null"]}, "starting_position": {"type": ["string", "null"]}, "special_tactics": {"type": ["string", "null"]}}, "required": ["monster_id", "quantity"]}}, "hazards": {"type": "array", "items": {"type": "object", "properties": {"hazard_id": {"type": "string", "description": "ID of the hazard entity"}, "location": {"type": ["string", "null"]}, "activation_condition": {"type": ["string", "null"]}}, "required": ["hazard_id"]}}, "treasure": {"type": "array", "items": {"type": "object", "properties": {"item_id": {"type": ["string", "null"]}, "description": {"type": "string"}, "value_gp": {"type": ["number", "null"]}, "hidden": {"type": "boolean", "default": false}, "discovery_dc": {"type": ["integer", "null"]}}, "required": ["description"]}}, "environmental_factors": {"type": "array", "items": {"type": "string"}, "description": "Environmental conditions affecting the encounter"}, "victory_conditions": {"type": "array", "items": {"type": "string"}, "description": "Conditions for successfully completing the encounter"}, "failure_consequences": {"type": "array", "items": {"type": "string"}, "description": "Consequences of failing the encounter"}, "scaling_suggestions": {"type": "object", "properties": {"easier": {"type": "string"}, "harder": {"type": "string"}}, "description": "Suggestions for scaling encounter difficulty"}}, "required": ["encounter_title", "encounter_type", "difficulty_rating", "party_level"]}, "condition_dnd5e_yaml": {"allOf": [{"$ref": "#/definitions/universal_dnd5e_fields"}], "type": "object", "properties": {"entity_type": {"const": "condition"}, "condition_type": {"type": "string", "enum": ["standard", "disease", "madness", "curse", "poison", "exhaustion", "other"], "description": "The type of condition this represents"}, "duration_type": {"type": "string", "enum": ["instantaneous", "until_end_of_turn", "until_start_of_turn", "1_minute", "10_minutes", "1_hour", "8_hours", "24_hours", "until_cured", "permanent", "special"], "description": "How long the condition typically lasts"}, "mechanical_effects": {"type": "array", "items": {"type": "string"}, "description": "List of mechanical effects this condition imposes"}, "saving_throw": {"type": "object", "properties": {"ability": {"type": "string", "enum": ["Strength", "Dexterity", "Constitution", "Intelligence", "Wisdom", "Charisma"]}, "dc": {"type": ["integer", "string"]}, "frequency": {"type": "string", "description": "e.g., 'at the end of each turn', 'once per day'"}}, "description": "Saving throw to resist or end the condition"}, "removal_methods": {"type": "array", "items": {"type": "string"}, "description": "Ways to remove or cure this condition"}, "onset_time": {"type": ["string", "null"], "description": "Time before condition takes effect (for diseases/poisons)"}, "symptoms": {"type": "array", "items": {"type": "string"}, "description": "Observable symptoms of this condition"}, "contagious": {"type": ["boolean", "null"], "description": "Whether this condition can spread to others"}, "stacking": {"type": "boolean", "default": false, "description": "Whether multiple instances of this condition can affect the same creature"}}, "required": ["condition_type", "mechanical_effects"]}}}