import logging
import os
import json
from typing import Dict, List, Optional # Added List

# Ensure .env is loaded for environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

import yaml
from yaml import YAMLError # For actual frontmatter parsing
import jsonschema # For schema validation
from jsonschema import validate, ValidationError, SchemaError

from src.models import ProcessedMarkdownResult # This might be unused if MarkdownProcessorService is removed or refactored

logger = logging.getLogger(__name__)

class MarkdownProcessorService:
    """
    Service for LLM-driven extraction, schema validation, and multi-file output
    of TTRPG content from Markdown chunks. This service acts as Pass 2 in a multi-pass system.
    """

    def __init__(self, narrative_schema_path: str, dnd5e_schema_path: str, output_root: str = "verticals/TTRPGContent/data"):
        """
        Initializes the MarkdownProcessorService.

        Args:
            narrative_schema_path: Path to the narrative TTRPG content JSON schema.
            dnd5e_schema_path: Path to the D&D 5e game-specific JSON schema.
            output_root: Root directory for structured output files.
        """
        self.output_root = output_root
        self.narrative_schema = self._load_schema(narrative_schema_path, "Narrative")
        self.dnd5e_schema = self._load_schema(dnd5e_schema_path, "D&D 5e")

        # Initialize dictionary to store game-specific schemas and LLM instructions
        self.game_schemas = {
            "dnd5e": self.dnd5e_schema
        }
        self.llm_instructions = {}

        # Load game-specific LLM instructions
        self._load_game_instructions("dnd5e")

        logger.info("MarkdownProcessorService (Pass 2) initialized.")

    def _load_schema(self, schema_path: str, schema_name: str) -> Optional[Dict]:
        try:
            with open(schema_path, "r", encoding="utf-8") as f:
                logger.info(f"Successfully loaded {schema_name} schema from {schema_path}")
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load {schema_name} schema from {schema_path}: {e}", exc_info=True)
            return None

    def _load_game_instructions(self, game_system: str):
        """Load LLM instructions for a specific game system."""
        instruction_path = f"config/llm_instructions/{game_system}_instructions.json"
        try:
            with open(instruction_path, "r", encoding="utf-8") as f:
                self.llm_instructions[game_system] = json.load(f)
                logger.info(f"Loaded LLM instructions for {game_system}")
        except FileNotFoundError:
            logger.warning(f"LLM instruction file not found: {instruction_path}")
            self.llm_instructions[game_system] = {}
        except Exception as e:
            logger.warning(f"Failed to load LLM instructions for {game_system}: {e}")
            self.llm_instructions[game_system] = {}

    def _is_game_system_supported(self, game_system: str, entity_type: str) -> bool:
        """Check if a game system is supported for a specific entity type."""
        # Narrative entities are always supported regardless of game system
        narrative_entity_types = [
            "campaign", "adventure", "adventure_section", "chapter", "location",
            "npc", "faction", "event", "quest"
        ]

        if entity_type in narrative_entity_types:
            return True

        # Game-specific entities require schema support
        game_specific_entity_types = [
            "monster", "item", "spell", "hazard", "condition", "class", "race",
            "background", "feat", "encounter", "magic_item"
        ]

        if entity_type in game_specific_entity_types:
            return game_system in self.game_schemas and self.game_schemas[game_system] is not None

        return False

    def process_markdown(self, content_chunk: str, overall_metadata: Dict, source_document_name: str, game_system: str) -> List[str]:
        """
        Extracts, validates, and outputs structured Markdown files from a Markdown content chunk.

        Args:
            content_chunk: The Markdown content string for the current chunk.
            overall_metadata: High-level metadata from Pass 1 (e.g., adventure_slug, title).
            source_document_name: The name of the original source document.
            game_system: The identified game system (e.g., "dnd5e", "universal_narrative").

        Returns:
            List of file paths to the created Markdown files for this chunk.
        """
        logger.info(f"Starting Pass 2 processing for a chunk from '{source_document_name}' with game system '{game_system}'.")

        # Check if we have support for this game system for game-specific entities
        if game_system not in self.game_schemas and game_system != "universal_narrative":
            logger.warning(f"Game system '{game_system}' is not supported for game-specific entities. Only narrative entities will be processed.")

        entities = self._call_llm_api(content_chunk, game_system, source_document_name, overall_metadata)

        main_adventure_slug = overall_metadata.get("adventure_slug")
        # Use the 'id' from overall_metadata if it's the adventure entity itself.
        main_adventure_id = overall_metadata.get("id") if overall_metadata.get("entity_type") == "adventure" else None


        created_files: List[str] = []
        for entity in entities:
            entity_type = entity.get("entity_type")
            yaml_data = dict(entity.get("yaml_data", {}))
            markdown_body = entity.get("markdown_body", "")

            if not entity_type or not yaml_data:
                logger.warning(f"Skipping entity due to missing entity_type or yaml_data: {entity}")
                continue

            # Check if this entity type is supported for the current game system
            if not self._is_game_system_supported(game_system, entity_type):
                error_msg = f"Unsupported combination: entity_type '{entity_type}' with game_system '{game_system}'. Skipping entity."
                logger.error(error_msg)
                continue

            name_slug = self._slugify(yaml_data.get("name", f"unknown-{entity_type}"))
            
            # Ensure adventure_slug and parent_adventure_id are correctly set
            if main_adventure_slug:
                yaml_data["adventure_slug"] = main_adventure_slug # Always set if main adventure context exists
                if entity_type != "adventure" and main_adventure_id: # Don't set parent_id for the adventure itself
                    yaml_data["parent_adventure_id"] = main_adventure_id

            # Construct ID
            if entity_type == "adventure" and main_adventure_slug: # This is the main adventure entity
                 id_val = main_adventure_id or f"adventure.{game_system}.{main_adventure_slug}"
            elif main_adventure_slug: # Entities within the main adventure
                id_val = f"{main_adventure_slug}.{entity_type}.{name_slug}"
            else: # Generic entities
                id_val = f"{entity_type}.{game_system}.{name_slug}"
            
            yaml_data["id"] = id_val
            yaml_data["entity_type"] = entity_type
            yaml_data["game_system"] = game_system
            yaml_data["source_book"] = source_document_name

            # Add default organizational hierarchy and relationships for standalone adventures
            self._add_default_organizational_structure(yaml_data, entity_type, overall_metadata)

            # Schema Validation
            validation_errors: List[str] = []
            is_valid = True
            chosen_schema_for_validation: Optional[Dict] = None
            sub_schema_key_for_validation: Optional[str] = None

            # Updated entity type mappings for all narrative entity types with schema definitions
            narrative_entity_types = [
                "campaign", "adventure", "adventure_section", "chapter", "location",
                "npc", "faction", "event", "quest"
            ]

            if entity_type in narrative_entity_types:
                chosen_schema_for_validation = self.narrative_schema
                if entity_type == "campaign": sub_schema_key_for_validation = "campaign_yaml"
                elif entity_type == "adventure": sub_schema_key_for_validation = "adventure_yaml"
                elif entity_type == "adventure_section": sub_schema_key_for_validation = "adventure_section_yaml"
                elif entity_type == "chapter": sub_schema_key_for_validation = "chapter_yaml"
                elif entity_type == "location": sub_schema_key_for_validation = "location_narrative_yaml"
                elif entity_type == "npc": sub_schema_key_for_validation = "npc_narrative_yaml"
                elif entity_type == "faction": sub_schema_key_for_validation = "faction_yaml"
                elif entity_type == "event": sub_schema_key_for_validation = "event_yaml"
                elif entity_type == "quest": sub_schema_key_for_validation = "quest_yaml"
            elif entity_type in ["monster", "item", "spell", "hazard", "condition", "class", "race", "background", "feat", "encounter", "magic_item"]:
                # Get the game-specific schema based on the game_system
                game_schema = self.game_schemas.get(game_system)

                if not game_schema:
                    error_msg = f"Error: No schema available for game system '{game_system}'. Processing cannot continue for entity type '{entity_type}'."
                    logger.error(error_msg)
                    validation_errors.append(error_msg)
                    is_valid = False
                    continue

                chosen_schema_for_validation = game_schema
                # Construct the sub-schema key dynamically based on entity_type and game_system
                sub_schema_key_for_validation = f"{entity_type}_{game_system}_yaml"

                # Check if the constructed key exists in the schema
                if "definitions" in game_schema and sub_schema_key_for_validation not in game_schema["definitions"]:
                    error_msg = f"Error: No schema definition found for '{sub_schema_key_for_validation}' in the {game_system} schema."
                    logger.error(error_msg)
                    validation_errors.append(error_msg)
                    is_valid = False
                    continue
            else:
                logger.warning(f"No specific schema mapping for entity_type='{entity_type}', game_system='{game_system}'. Validation will be skipped for {id_val}.")
                is_valid = True # Or False if strict validation is required for all types

            if chosen_schema_for_validation and sub_schema_key_for_validation:
                if "definitions" in chosen_schema_for_validation and sub_schema_key_for_validation in chosen_schema_for_validation["definitions"]:
                    try:
                        from jsonschema import Draft7Validator, RefResolver
                        resolver = RefResolver.from_schema(chosen_schema_for_validation)
                        validator = Draft7Validator(chosen_schema_for_validation["definitions"][sub_schema_key_for_validation], resolver=resolver)
                        validator.validate(yaml_data)
                        logger.info(f"Entity {id_val} validated successfully against sub-schema {sub_schema_key_for_validation}.")
                    except ValidationError as e: # ... (error handling)
                        is_valid = False
                        error_detail = f"Validation Error for {id_val} against {sub_schema_key_for_validation}: {e.message}"
                        if e.path: error_detail += f" (at path: {list(e.path)})"
                        validation_errors.append(error_detail)
                    except SchemaError as e:
                        is_valid = False; validation_errors.append(f"Schema Error for {sub_schema_key_for_validation}: {e.message}")
                    except Exception as e:
                        is_valid = False; validation_errors.append(f"Unexpected validation error for {id_val} with {sub_schema_key_for_validation}: {str(e)}")
                else:
                    logger.warning(f"Sub-schema key '{sub_schema_key_for_validation}' not found in definitions of chosen schema for {id_val}.")
                    is_valid = False
            elif chosen_schema_for_validation and not sub_schema_key_for_validation:
                 logger.warning(f"Chosen schema for {id_val} but no sub_schema_key determined. Entity Type: {entity_type}")
                 is_valid = False
            
            if not is_valid:
                logger.error(f"Validation failed for entity {id_val}: {validation_errors}")
                continue

            # Determine output path
            entity_type_plural = self._pluralize_entity_type(entity_type)
            current_adventure_slug_for_path = yaml_data.get("adventure_slug") # Use the slug from the entity's own data

            if current_adventure_slug_for_path: # If entity has an adventure_slug (likely part of an adventure)
                if entity_type == "adventure": 
                     out_dir = os.path.join(self.output_root, game_system, "adventures", current_adventure_slug_for_path)
                else: 
                     out_dir = os.path.join(self.output_root, game_system, "adventures", current_adventure_slug_for_path, entity_type_plural)
            else: # Generic entities
                out_dir = os.path.join(self.output_root, game_system, entity_type_plural)
            
            os.makedirs(out_dir, exist_ok=True)
            filename = f"{yaml_data['id']}.md" # Use the final ID for filename
            out_path = os.path.join(out_dir, filename)

            yaml_str = yaml.safe_dump(yaml_data, sort_keys=False, allow_unicode=True, default_flow_style=False, indent=2)
            with open(out_path, "w", encoding="utf-8") as f:
                f.write(f"---\n{yaml_str}---\n\n{markdown_body.strip()}\n")
            created_files.append(out_path)
            logger.info(f"Successfully wrote entity file: {out_path}")

        return created_files

    def _call_llm_api(self, content_chunk: str, game_system: str, source_document_name: str, overall_metadata: Dict):
        """
        Calls the LLM API to extract structured TTRPG entities from a Markdown CHUNK.
        Uses context from overall_metadata and includes game-specific instructions.
        """
        import os
        import json

        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            logger.error("OPENAI_API_KEY not set in environment.")
            return []

        adventure_slug_context = overall_metadata.get('adventure_slug', 'N/A')
        adventure_title_context = overall_metadata.get('adventure_title', 'N/A')
        # Use the ID from overall_metadata if it's the adventure entity, otherwise it's not directly relevant for parent_adventure_id here
        parent_adventure_id_context = overall_metadata.get('id') if overall_metadata.get('entity_type') == 'adventure' else 'UNKNOWN_ADVENTURE_ID'

        # Get game-specific instructions if available
        game_instructions_section = ""
        if game_system in self.llm_instructions and self.llm_instructions[game_system]:
            game_instructions_section = f"\n\n**Game-Specific Extraction Instructions for {game_system.upper()}:**\n"
            for entity_type, instruction in self.llm_instructions[game_system].items():
                game_instructions_section += f"- **{entity_type}**: {instruction}\n"


        prompt = f"""
You are an expert TTRPG content analyst. Your task is to process the following TTRPG Markdown CHUNK, identify distinct game entities within this chunk, and extract their information into a structured JSON format. This chunk is part of a larger document: '{source_document_name}', which has been identified as a '{overall_metadata.get('identified_document_type', 'unknown type')}' for the '{game_system}' game system. The adventure slug for context is '{adventure_slug_context}' and the adventure title is '{adventure_title_context}'. The main adventure's ID is '{parent_adventure_id_context}'.{game_instructions_section}

**Input Markdown CHUNK:**
```
{content_chunk}
```

**Schema Guidance:**
- For NARRATIVE entities (types: "campaign", "adventure", "adventure_section", "chapter", "location", "npc", "faction", "event", "quest"), use fields defined in `narrative_entities_schema.json`.
  - ALL narrative entities MUST include: `organizational_hierarchy`, `relationships`, `content_organization` objects
  - Key `adventure_yaml` fields: `adventure_title`, `plot_summary_narrative`, `setting_overview`, `plot_hook`
  - Key `adventure_section_yaml` fields: `parent_adventure_id`, `section_title`, `description_environment_narrative`, `key_npcs_narrative`, `suggested_mechanics_by_system`
  - Key `chapter_yaml` fields: `chapter_title`, `parent_adventure_id`, `chapter_summary`, `chapter_objectives`
  - Key `location_narrative_yaml` fields: `location_title`, `parent_adventure_id`, `description_general_narrative`, `points_of_interest_narrative`, `location_type`, `location_scale`
  - Key `npc_narrative_yaml` fields: `npc_full_name_or_title`, `parent_adventure_id`, `role_in_adventure_narrative`, `description_physical_narrative`, `suggested_mechanics_by_system`
  - Key `faction_yaml` fields: `faction_name`, `faction_type`, `power_level`, `primary_goals`
  - Key `event_yaml` fields: `event_title`, `event_type`, `event_description`, `trigger_conditions`
  - Key `quest_yaml` fields: `quest_title`, `quest_type`, `objectives`, `rewards`
- For D&D 5e SPECIFIC entities (types: "monster", "item", "spell", "hazard"), use fields defined in `dnd5e_entities_schema.json`.
  - Key `monster_dnd5e_yaml` fields: `size`, `monster_type`, `ac`, `hp`, `speed`, `ability_scores` (object with str, dex, etc.), `challenge_rating`, `actions`.
  - Key `item_dnd5e_yaml` fields: `item_type_dnd5e`, `rarity_dnd5e`, `description_mechanical`.
  - Key `spell_dnd5e_yaml` fields: `spell_level`, `school_of_magic`, `casting_time`, `range`, `components`, `duration`, `description_full`.

**Instructions for this CHUNK:**
1. Identify Entities: Scan THIS CHUNK for distinct TTRPG entities.
   - If this chunk describes a specific scene, encounter, or sub-location within the main adventure (e.g., '### 1. The Arch Encounter', '## Area 4: The Ruined Library'), extract it as an `adventure_section` or `location` entity.
   - For chapters/sections: Use `chapter` for major divisions, `adventure_section` for smaller encounters/areas
   - For locations: Use `location_type` (settlement, building, room, area, region, dungeon, wilderness, structure) and `location_scale` (tiny, small, medium, large, huge, vast)
   - For NPCs: Include `faction_affiliation_narrative` if they belong to organizations
   - For `monster` entities (game_system: dnd5e):
     - `ability_scores` MUST be an object: `{{"str": 10, "dex": 12, ...}}`.
     - `skills_proficiency` items use `"bonus"` (integer).
     - `senses` MUST be an object: `{{"darkvision": "60 ft.", "passive_perception": 10}}`.
   - For `npc` entities:
     - If using standard stats (e.g., "noble stats"), use `stats_id` (e.g., "monster.dnd5e.noble"). Do NOT create a custom stat_block.
     - List unique abilities in `key_abilities` (narrative schema) or `special_abilities` (dnd5e schema if applicable).
     - Create `npc` entities for all named characters with dialogue/roles.
   - For `item` entities:
     - Prioritize items with prominent sub-headings and detailed descriptions.
2. Populate `yaml_data`:
   - `entity_type`: (e.g., "monster", "spell", "npc", "location", "adventure_section", "chapter", "faction", "event", "quest").
   - `name`: Human-readable name.
   - `game_system`: Set to `{game_system}`.
   - `source_book`: Set to `{source_document_name}`.
   - `adventure_slug`: If the entity is part of the main adventure context (slug: '{adventure_slug_context}'), set this to `{adventure_slug_context}`.
   - `parent_adventure_id`: For narrative entities that are part of the main adventure, set this to `{parent_adventure_id_context}`.
   - For narrative entities, include organizational structure:
     - `organizational_hierarchy`: Set `campaign_id` to null for standalone adventures, `module_id` to parent adventure ID
     - `relationships`: Include `contains`, `contained_by`, `references`, `referenced_by`, `depends_on`, `enables` arrays/values
     - `content_organization`: Set `content_type` (narrative/mechanical/reference/background/plot/encounter), `complexity_level`, `display_order`
   - Adhere strictly to the field names and structures from the schemas mentioned above.
3. `markdown_body`: The narrative Markdown content specifically belonging to THIS entity from THIS CHUNK.
4. Output Format: Return a JSON object. This object MUST contain a single top-level key named "result". The value of "result" MUST be a JSON list of objects, where each object represents one extracted entity:
   `{{"result": [ {{"entity_type": "...", "yaml_data": {{...}}, "markdown_body": "..."}} ] }}`
"""
        logger.debug(f"Pass 2 LLM Prompt for chunk of '{source_document_name}':\n{prompt[:500]}...") 

        try:
            import openai
            client = openai.OpenAI(api_key=api_key)
            response = client.chat.completions.create(
                model="gpt-4.1-mini-2025-04-14", # Consider making model configurable
                messages=[
                    {"role": "system", "content": "You are a helpful assistant for TTRPG content extraction."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,
                max_tokens=16384, # Consider if this is per chunk or total
                response_format={"type": "json_object"}
            )
            content = response.choices[0].message.content
            logger.info(f"LLM raw response content for chunk:\n{content[:500]}...") # Log part of response
            # print("LLM raw response content:\n", content) # Potentially too verbose for many chunks
            parsed_json = json.loads(content)
            if isinstance(parsed_json, dict) and "result" in parsed_json and isinstance(parsed_json["result"], list):
                return parsed_json["result"]
            else:
                logger.error(f"LLM response JSON for chunk does not have the expected structure. Received: {parsed_json}")
                return []
        except ImportError:
            logger.error("openai package not installed. Please install with 'pip install openai'.")
            return []
        except Exception as e:
            logger.error(f"LLM API call for chunk failed: {e}", exc_info=True)
            return []

    def _call_llm_mock(self, content_chunk: str, game_system: str, source_document_name: str, overall_metadata: Dict):
        """
        Mocked LLM call for Pass 2: returns a hardcoded list of entity dicts for testing.
        This mock should be updated to reflect chunk-based processing and schema usage.
        """
        logger.warning(f"Using MOCKED LLM call for Pass 2 for chunk: {content_chunk[:100]}...")
        # This mock needs to be more sophisticated to be useful for testing the new logic.
        # For now, it returns a generic structure.
        adventure_slug_context = overall_metadata.get('adventure_slug', 'mock_adventure_slug')
        parent_adventure_id_context = overall_metadata.get('id', 'mock_parent_adv_id')

        mock_entities = []
        if "Goblin Sentry" in content_chunk:
            mock_entities.append({
                "entity_type": "monster",
                "yaml_data": {
                    "name": "Goblin Sentry (Mock)",
                    "game_system": game_system,
                    "source_book": source_document_name,
                    "adventure_slug": adventure_slug_context,
                    "size": "Small", "monster_type": "humanoid (goblinoid)", "alignment": "neutral evil",
                    "ac": 13, "hp": 7, "speed": {"walk": "30 ft."},
                    "ability_scores": {"str": 8, "dex": 14, "con": 10, "int": 10, "wis": 8, "cha": 8},
                    "challenge_rating": "1/4", "xp": 50
                },
                "markdown_body": "A mock Goblin Sentry stands watch."
            })
        if "Potion of Healing" in content_chunk:
             mock_entities.append({
                "entity_type": "item",
                "yaml_data": {
                    "name": "Potion of Healing (Mock)",
                    "game_system": game_system,
                    "source_book": source_document_name,
                    "adventure_slug": adventure_slug_context,
                    "item_type_dnd5e": "Potion", "rarity_dnd5e": "Common",
                    "description_mechanical": "Restores 2d4+2 hit points."
                },
                "markdown_body": "A mock Potion of Healing."
            })
        if "Gribnock the Goblin Boss" in content_chunk:
            mock_entities.append({
                "entity_type": "npc",
                "yaml_data": {
                    "name": "Gribnock the Goblin Boss (Mock)",
                    "game_system": game_system, # Should be universal_narrative if using narrative schema
                    "source_book": source_document_name,
                    "adventure_slug": adventure_slug_context,
                    "parent_adventure_id": parent_adventure_id_context,
                    "npc_full_name_or_title": "Gribnock the Goblin Boss (Mock)",
                    "role_in_adventure_narrative": "The main antagonist of this mock chunk."
                },
                "markdown_body": "Gribnock the mock Goblin Boss."
            })
        
        if not mock_entities and "Test Adventure" in content_chunk: # If it's the first chunk with title
             mock_entities.append({
                "entity_type": "adventure", # This should align with overall_metadata from Pass 1
                "yaml_data": {
                    "name": overall_metadata.get("adventure_title", "Mock Adventure Title"),
                    "adventure_title": overall_metadata.get("adventure_title", "Mock Adventure Title"),
                    "adventure_slug": adventure_slug_context,
                    "game_system": game_system,
                    "source_book": source_document_name,
                    "plot_summary_narrative": "A mock adventure summary from Pass 2."
                    # id will be set by the main logic
                },
                "markdown_body": "This is the main body for the mock adventure overview."
            })


        return mock_entities

    def _add_default_organizational_structure(self, yaml_data: Dict, entity_type: str, overall_metadata: Dict):
        """
        Adds default organizational hierarchy, relationships, and content organization
        fields for narrative entities in standalone adventures.
        """
        # Only add defaults for narrative entity types with schema definitions
        narrative_entity_types = [
            "campaign", "adventure", "adventure_section", "chapter", "location",
            "npc", "faction", "event", "quest"
        ]

        if entity_type not in narrative_entity_types:
            return

        # Add organizational_hierarchy if not present
        if "organizational_hierarchy" not in yaml_data:
            yaml_data["organizational_hierarchy"] = {}

        org_hierarchy = yaml_data["organizational_hierarchy"]

        # Set default hierarchy values for standalone adventures
        if entity_type == "adventure":
            # Adventures can be standalone (no campaign) or part of a campaign
            if "campaign_id" not in org_hierarchy:
                org_hierarchy["campaign_id"] = None
            if "module_id" not in org_hierarchy:
                org_hierarchy["module_id"] = None
        elif entity_type in ["adventure_section", "chapter", "location", "npc", "event", "quest"]:
            # These entities typically belong to an adventure
            parent_adventure_id = yaml_data.get("parent_adventure_id") or overall_metadata.get("id")

            if "campaign_id" not in org_hierarchy:
                org_hierarchy["campaign_id"] = None  # Standalone adventure
            if "module_id" not in org_hierarchy:
                org_hierarchy["module_id"] = parent_adventure_id

        # Add relationships if not present
        if "relationships" not in yaml_data:
            yaml_data["relationships"] = {
                "contains": [],
                "contained_by": None,
                "references": [],
                "referenced_by": [],
                "depends_on": [],
                "enables": []
            }

        # Add content_organization if not present
        if "content_organization" not in yaml_data:
            yaml_data["content_organization"] = {
                "display_order": None,
                "content_type": "narrative",  # Default for most narrative entities
                "complexity_level": "moderate",  # Default complexity
                "estimated_duration_minutes": None,
                "prerequisites": []
            }

        # Set specific content_type defaults based on entity_type
        content_org = yaml_data["content_organization"]
        if "content_type" not in content_org or content_org["content_type"] == "narrative":
            if entity_type == "adventure_section":
                content_org["content_type"] = "encounter"
            elif entity_type == "location":
                content_org["content_type"] = "reference"
            elif entity_type == "npc":
                content_org["content_type"] = "reference"
            elif entity_type == "event":
                content_org["content_type"] = "plot"
            elif entity_type == "quest":
                content_org["content_type"] = "plot"
            elif entity_type == "faction":
                content_org["content_type"] = "background"

    def _slugify(self, text: str) -> str:
        import re
        import unicodedata
        text = unicodedata.normalize("NFKD", text).encode("ascii", "ignore").decode("ascii")
        text = text.lower()
        text = re.sub(r"[^\w\s-]", "", text)
        text = re.sub(r"[\s_-]+", "-", text)
        text = re.sub(r"^-+|-+$", "", text)
        return text

    def _pluralize_entity_type(self, entity_type: str) -> str:
        irregulars = {
            # Narrative entity types
            "campaign": "campaigns", "adventure": "adventures", "adventure_section": "adventure_sections",
            "chapter": "chapters", "location": "locations", "npc": "npcs", "faction": "factions",
            "event": "events", "quest": "quests",
            # D&D 5e entity types
            "monster": "monsters", "item": "items", "spell": "spells", "hazard": "hazards",
            "class": "classes", "race": "races", "background": "backgrounds",
            "condition": "conditions", "feat": "feats",
            # Legacy mappings
            "magic_item": "items", "adventure_overview": "adventures", "rule_section": "rules",
            "world_lore": "world_lore", "item_narrative": "items_narrative",
            "lore": "lore", "timeline_entry": "timeline_entries"
        }
        if entity_type in irregulars:
            return irregulars[entity_type]
        if entity_type.endswith("y"):
            return entity_type[:-1] + "ies"
        return entity_type + "s"
