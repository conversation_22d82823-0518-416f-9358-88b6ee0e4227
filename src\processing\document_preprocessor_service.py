import logging
import os
import json
from typing import Dict, List, Optional

# Ensure .env is loaded for environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

import re # For header-based chunking
from src.models import DocumentAnalysisResult

logger = logging.getLogger(__name__)

# A rough estimate, 1 token ~ 4 chars.
# The example adventure "The Light of Memoria" is roughly 45k characters.
# If we aim for chunks around 16k tokens (GPT-4-turbo context is 128k, but we want smaller for focused processing)
# 16000 tokens * 4 chars/token = 64000 characters.
# Let's set a threshold slightly above the example doc for now.
# This means the example doc itself would NOT be chunked by this simple length check.
# A more sophisticated token counting method (e.g., tiktoken) would be better for production.
ROUGH_CHAR_THRESHOLD_FOR_CHUNKING = 70000 # Characters

class DocumentPreProcessorService:
    """
    Service for the first pass of LLM processing.
    It analyzes the overall document, extracts high-level metadata,
    and determines if chunking is necessary.
    """

    def __init__(self, narrative_schema_path: str):
        """
        Initializes the DocumentPreProcessorService.

        Args:
            narrative_schema_path: Path to the narrative TTRPG content JSON schema
                                   (used for understanding adventure_yaml structure).
        """
        self.narrative_schema_path = narrative_schema_path
        self.narrative_schema = self._load_schema(narrative_schema_path)
        logger.info("DocumentPreProcessorService initialized.")

    def _load_schema(self, schema_path: str) -> Optional[Dict]:
        try:
            with open(schema_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load schema from {schema_path}: {e}", exc_info=True)
            return None

    def _estimate_requires_chunking(self, raw_md_content: str) -> bool:
        """
        Basic estimation if chunking is required based on content length.
        A more sophisticated method (e.g., tiktoken) should be used for accuracy.
        """
        return len(raw_md_content) > ROUGH_CHAR_THRESHOLD_FOR_CHUNKING

    def _chunk_by_headers(self, raw_md_content: str) -> List[str]:
        """
        Splits the document by H1 (##) and H2 (###) headers.
        Each chunk includes the header that started it.
        Headers themselves are not split.
        """
        # Regex to find H1 (starting with #) or H2 (starting with ##)
        # and capture the header line and the content until the next H1/H2 or end of doc.
        # (?m) for multiline, (^\#{1,2}\s.*) captures H1/H2 header line
        # ([\s\S]*?) non-greedily captures content until the lookahead
        # (?=(?:^\#{1,2}\s.*|$)) lookahead for next H1/H2 or end of string
        chunks = []
        current_chunk_content = []
        current_header = None

        # Split by lines to handle headers correctly
        lines = raw_md_content.splitlines(keepends=True)
        
        # Find all H1 and H2 headers and their starting line indices
        header_indices = []
        for i, line in enumerate(lines):
            if re.match(r"^\#{1,2}\s", line): # H1 or H2
                 header_indices.append(i)
        
        if not header_indices: # No H1/H2 headers, return as single chunk
            return [raw_md_content]

        # Create chunks based on header indices
        start_index = 0
        # If the document doesn't start with an H1/H2, the content before the first H1/H2 is one chunk.
        if header_indices[0] != 0:
            chunks.append("".join(lines[0:header_indices[0]]))
            start_index = header_indices[0]
        
        for i in range(len(header_indices)):
            chunk_start_line = header_indices[i]
            chunk_end_line = header_indices[i+1] if (i + 1) < len(header_indices) else len(lines)
            chunks.append("".join(lines[chunk_start_line:chunk_end_line]))
            
        # Filter out empty chunks that might result from consecutive headers
        return [chunk for chunk in chunks if chunk.strip()]

    def _detect_game_system(self, content: str, initial_hint: str) -> str:
        """
        Detect game system based on explicit mentions in the document.
        Adventure modules typically clearly state their game system.
        """
        content_lower = content.lower()

        # Look at the first 2000 characters where game system is usually mentioned
        header_content = content[:2000].lower()

        # Direct game system mentions (most reliable)
        if any(phrase in header_content for phrase in [
            "d&d 5e", "d&d 5th edition", "dungeons & dragons 5e",
            "dungeons & dragons 5th edition", "fifth edition",
            "5e compatible", "for 5e", "dnd 5e", "dnd5e"
        ]):
            logger.info("Detected D&D 5e from explicit mention")
            return "dnd5e"

        if any(phrase in header_content for phrase in [
            "pathfinder 2e", "pathfinder second edition", "pf2e", "p2e",
            "pathfinder 2nd edition"
        ]):
            logger.info("Detected Pathfinder 2e from explicit mention")
            return "pathfinder2e"

        if any(phrase in header_content for phrase in [
            "pathfinder", "pathfinder rpg", "pfrpg"
        ]) and "2e" not in header_content and "second" not in header_content:
            logger.info("Detected Pathfinder 1e from explicit mention")
            return "pathfinder1e"

        if any(phrase in header_content for phrase in [
            "call of cthulhu", "coc", "chaosium"
        ]):
            logger.info("Detected Call of Cthulhu from explicit mention")
            return "call_of_cthulhu"

        if any(phrase in header_content for phrase in [
            "savage worlds", "swade"
        ]):
            logger.info("Detected Savage Worlds from explicit mention")
            return "savage_worlds"

        # Look for D&D 5e mechanical indicators in the full content
        dnd5e_indicators = [
            r'\bac\s+\d+\b',  # Armor Class
            r'\bhp\s+\d+\b',  # Hit Points
            r'\bchallenge\s+\d+',  # Challenge Rating
            r'\bproficiency\s+bonus',
            r'\bspell\s+save\s+dc',
            r'\blegendary\s+action',
            r'\bpassive\s+perception\b'
        ]

        dnd5e_score = sum(1 for pattern in dnd5e_indicators if re.search(pattern, content_lower))

        if dnd5e_score >= 2:
            logger.info(f"Detected D&D 5e from mechanical indicators (score: {dnd5e_score})")
            return "dnd5e"

        # If we have a reasonable initial hint, use it
        if initial_hint and initial_hint != "unknown":
            logger.info(f"Using provided game system hint: {initial_hint}")
            return initial_hint

        # Default to D&D 5e as it's the most common
        logger.info("Could not detect specific game system, defaulting to dnd5e")
        return "dnd5e"

    def _call_pass1_llm_api(self, raw_md_content: str, initial_game_system: str, source_document_name: str) -> Dict:
        """
        Pass 1 analysis: Extract metadata and detect game system from document content.
        This analyzes the document to determine game system, title, and document type.
        """
        logger.info("Executing Pass 1 document analysis...")

        # Try to find a title from the first H1
        title_match = re.search(r"^\#\s*(.*)", raw_md_content, re.MULTILINE)
        doc_title = title_match.group(1).strip() if title_match else source_document_name

        # Detect the actual game system from content
        detected_game_system = self._detect_game_system(raw_md_content, initial_game_system)

        # Basic slugification
        slug = doc_title.lower().replace(" ", "-").replace(":", "").replace("'", "")
        slug = re.sub(r'[^a-z0-9-]+', '', slug)

        # Determine document type based on content and title
        doc_type = "adventure" # Default assumption
        if "rule" in doc_title.lower() or "system" in doc_title.lower():
            doc_type = "rulebook"
        elif "bestiary" in doc_title.lower() or "monster" in doc_title.lower():
            doc_type = "bestiary"

        logger.info(f"Pass 1 analysis complete - Game System: {detected_game_system}, Document Type: {doc_type}")

        return {
            "overall_metadata": {
                "adventure_title": doc_title, # Extracted from first H1 or source_document_name
                "adventure_slug": slug, # Simple slug based on title
                "game_system": detected_game_system, # Use detected game system
                "source_book": source_document_name,
                "entity_type": "adventure", # Assuming the whole doc is an adventure for this metadata
                "name": doc_title, # For consistency with narrative_schema
                "id": f"adventure.{detected_game_system}.{slug}", # Use detected game system
                "plot_summary_narrative": f"A summary of {doc_title}.", # Placeholder
                # Add other fields from adventure_yaml as needed by LLM
            },
            "identified_game_system": detected_game_system, # Use detected game system
            "identified_document_type": doc_type # Determined from content analysis
        }

    def analyze_document(self, raw_md_content: str, source_document_name: str, initial_game_system: str) -> DocumentAnalysisResult:
        """
        Analyzes the raw Markdown document.

        Args:
            raw_md_content: The full Markdown content as a string.
            source_document_name: The name of the source document.
            initial_game_system: An initial hint for the game system.

        Returns:
            A DocumentAnalysisResult object.
        """
        logger.info(f"Starting Pass 1 analysis for document: {source_document_name}")

        # 1. Call LLM for high-level metadata and document type
        llm_pass1_output = self._call_pass1_llm_api(raw_md_content, initial_game_system, source_document_name)
        
        overall_metadata = llm_pass1_output["overall_metadata"]
        identified_game_system = llm_pass1_output["identified_game_system"]
        identified_document_type = llm_pass1_output["identified_document_type"]

        # 2. Determine if chunking is needed
        requires_chunking = self._estimate_requires_chunking(raw_md_content)
        logger.info(f"Document requires_chunking: {requires_chunking} (based on char count > {ROUGH_CHAR_THRESHOLD_FOR_CHUNKING})")

        # 3. Perform chunking if needed
        content_chunks: List[str]
        if requires_chunking:
            logger.info("Performing chunking by H1/H2 headers.")
            content_chunks = self._chunk_by_headers(raw_md_content)
            if not content_chunks: # Safety net if chunking returns empty
                logger.warning("Chunking resulted in no content, falling back to single chunk.")
                content_chunks = [raw_md_content]
            logger.info(f"Document split into {len(content_chunks)} chunks.")
        else:
            logger.info("No chunking required, document will be processed as a single chunk.")
            content_chunks = [raw_md_content]
        
        # Ensure overall_metadata has the adventure_slug, as it's crucial
        if "adventure_slug" not in overall_metadata or not overall_metadata["adventure_slug"]:
            # Fallback slug if LLM didn't provide a good one
            fallback_slug = source_document_name.lower().replace(" ", "-")
            fallback_slug = re.sub(r'[^a-z0-9-]+', '', fallback_slug)
            overall_metadata["adventure_slug"] = fallback_slug if fallback_slug else "unknown-adventure"
            logger.warning(f"adventure_slug was missing or empty in LLM output, using fallback: {overall_metadata['adventure_slug']}")


        return DocumentAnalysisResult(
            overall_metadata=overall_metadata,
            content_chunks=content_chunks,
            requires_chunking=requires_chunking,
            identified_game_system=identified_game_system,
            identified_document_type=identified_document_type,
            source_document_name=source_document_name
        )

if __name__ == '__main__':
    # Example Usage (for testing this service in isolation)
    # This would typically be orchestrated by MultiPassOrchestrator
    load_dotenv() # Ensure .env is loaded if OPENAI_API_KEY is used by LLM
    
    mock_narrative_schema_path = "../../config/narrative_entities_schema.json" # Adjust path as needed
    # Create a dummy schema if it doesn't exist for this isolated test
    if not os.path.exists(mock_narrative_schema_path):
        os.makedirs(os.path.dirname(mock_narrative_schema_path), exist_ok=True)
        with open(mock_narrative_schema_path, "w") as f:
            json.dump({
                "$schema": "http://json-schema.org/draft-07/schema#",
                "title": "Mock Narrative Schema",
                "definitions": {
                    "adventure_yaml": {
                        "type": "object",
                        "properties": {
                            "adventure_title": {"type": "string"},
                            "adventure_slug": {"type": "string"}
                        }
                    }
                }
            }, f)

    preprocessor = DocumentPreProcessorService(narrative_schema_path=mock_narrative_schema_path)
    
    sample_md_content_short = """# My Short Adventure
    This is a very short adventure.
    ## Section 1
    Content for section 1.
    ## Section 2
    Content for section 2.
    """

    sample_md_content_long = "# My Long Adventure\n" + "This is a very long adventure section. " * 5000 + "\n## Chapter 1\nDetails of chapter 1. " * 1000 + "\n### Sub-Chapter 1.1\nMore details. " * 500 + "\n## Chapter 2\nDetails of chapter 2. " * 1000

    print("--- Analyzing Short Document ---")
    result_short = preprocessor.analyze_document(sample_md_content_short, "short_adventure_doc", "dnd5e")
    print(f"Requires Chunking: {result_short.requires_chunking}")
    print(f"Game System: {result_short.identified_game_system}")
    print(f"Document Type: {result_short.identified_document_type}")
    print(f"Adventure Slug: {result_short.overall_metadata.get('adventure_slug')}")
    print(f"Number of Chunks: {len(result_short.content_chunks)}")
    # for i, chunk in enumerate(result_short.content_chunks):
    #     print(f"Chunk {i+1}:\n{chunk[:200]}...\n")

    print("\n--- Analyzing Long Document ---")
    result_long = preprocessor.analyze_document(sample_md_content_long, "long_adventure_doc", "dnd5e")
    print(f"Requires Chunking: {result_long.requires_chunking}")
    print(f"Game System: {result_long.identified_game_system}")
    print(f"Document Type: {result_long.identified_document_type}")
    print(f"Adventure Slug: {result_long.overall_metadata.get('adventure_slug')}")
    print(f"Number of Chunks: {len(result_long.content_chunks)}")
    # for i, chunk in enumerate(result_long.content_chunks):
    #     print(f"Chunk {i+1}:\n{chunk[:200]}...\n")

    # Test with content that doesn't start with H1/H2
    sample_md_no_initial_header = """
    This is some introductory text without a main H1 or H2 header.
    It should become the first chunk.

    ## First Actual Section
    Content of the first actual section.

    ## Second Actual Section
    Content of the second actual section.
    """
    print("\n--- Analyzing Document with No Initial H1/H2 Header (but requires chunking due to length) ---")
    # Make it long enough to trigger chunking for test
    result_no_initial_header = preprocessor.analyze_document(sample_md_no_initial_header * 500, "no_initial_header_doc", "dnd5e")
    print(f"Requires Chunking: {result_no_initial_header.requires_chunking}")
    print(f"Number of Chunks: {len(result_no_initial_header.content_chunks)}")
    if result_no_initial_header.content_chunks:
        print(f"First chunk preview:\n{result_no_initial_header.content_chunks[0][:300]}...")
        if len(result_no_initial_header.content_chunks) > 1:
             print(f"Second chunk preview:\n{result_no_initial_header.content_chunks[1][:300]}...")


    sample_md_only_h3 = """
    # Main Title (H1)
    Some intro.
    ### Sub Section A (H3)
    Content A.
    ### Sub Section B (H3)
    Content B.
    ## Next Big Section (H2)
    More content.
    """
    print("\n--- Analyzing Document with H3s (should not split by H3) ---")
    result_h3 = preprocessor.analyze_document(sample_md_only_h3 * 500, "h3_test_doc", "dnd5e") # Multiply to ensure chunking
    print(f"Requires Chunking: {result_h3.requires_chunking}")
    print(f"Number of Chunks: {len(result_h3.content_chunks)}")
    # for i, chunk in enumerate(result_h3.content_chunks):
    #     print(f"Chunk {i+1} starts with: {chunk.splitlines()[0] if chunk.splitlines() else 'EMPTY CHUNK'}")
