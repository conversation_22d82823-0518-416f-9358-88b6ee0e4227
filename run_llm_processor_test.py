import os
import sys
import logging
import re

# Configure basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

sys.path.insert(0, os.getcwd())

from src.orchestration.multi_pass_orchestrator import MultiPassOrchestrator

def main():
    md_path = 'output/Warlock-Lair-The-Light-of-Memoria-zlxriy/Warlock-Lair-The-Light-of-Memoria-zlxriy.images-processed.md'
    # Define paths to the schemas
    narrative_schema_path = 'config/narrative_entities_schema.json'
    dnd5e_schema_path = 'config/dnd5e_entities_schema.json'
    
    output_root = 'verticals/TTRPGContent/data_multi_pass'
    
    # This is just a hint - Pass 1 should determine the actual game system from the content
    initial_game_system = 'unknown'  
    source_document_name = 'Warlock-Lair-The-Light-of-Memoria-zlxriy'
    
    # Verify that the instructions file exists for dnd5e (assuming the document is likely dnd5e)
    instructions_path = 'config/llm_instructions/dnd5e_instructions.json'
    if not os.path.exists(instructions_path):
        logger.warning(f"dnd5e instructions file not found at {instructions_path}")
        logger.info("The service should handle missing instruction files gracefully")

    logger.info(f"Starting LLM processing test for: {source_document_name}")
    logger.info(f"Markdown file: {md_path}")
    logger.info(f"Narrative Schema: {narrative_schema_path}")
    logger.info(f"D&D 5e Schema: {dnd5e_schema_path}")
    logger.info(f"Output root: {output_root}")

    # Ensure output directory exists
    os.makedirs(output_root, exist_ok=True)

    try:
        with open(md_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
        logger.info(f"Successfully read markdown content from {md_path}")
    except FileNotFoundError:
        logger.error(f"Markdown file not found: {md_path}")
        return
    except Exception as e:
        logger.error(f"Error reading markdown file {md_path}: {e}", exc_info=True)
        return

    # Instantiate the MultiPassOrchestrator
    orchestrator = MultiPassOrchestrator(
        narrative_schema_path=narrative_schema_path,
        dnd5e_schema_path=dnd5e_schema_path,
        output_root=output_root
    )

    logger.info("Invoking orchestrator process_document...")
    try:
        created_files = orchestrator.process_document(
            raw_md_content=md_content,
            source_document_name=source_document_name,
            initial_game_system=initial_game_system  # Just a hint, Pass 1 should determine the actual game system
        )
        logger.info(f"Orchestrator process_document completed. {len(created_files)} files reported as created.")
        
        # Try to determine which game system was actually used from the created files
        detected_game_systems = set()
        for file_path in created_files:
            # Look for game system identifiers in file paths
            # Example: "adventure.dnd5e.goblin-gauntlet.md"
            match = re.search(r'\.([a-z0-9_]+)\.', os.path.basename(file_path))
            if match and match.group(1) not in ['md', 'json', 'txt']:
                potential_game_system = match.group(1)
                if potential_game_system not in ['adventure', 'location', 'npc', 'monster', 'item', 'spell']:
                    detected_game_systems.add(potential_game_system)
        
        if detected_game_systems:
            logger.info(f"Detected game systems from output files: {', '.join(detected_game_systems)}")
        else:
            logger.info("Could not determine game system from output files")
        
        # Verify if any game-specific entities were created
        game_specific_types = ["monster", "spell", "item", "hazard", "condition", 
                              "class", "race", "background", "feat", "encounter", "magic_item"]
        
        for file_path in created_files:
            for entity_type in game_specific_types:
                if f".{entity_type}." in file_path:
                    logger.info(f"Found game-specific entity: {file_path}")
    except Exception as e:
        logger.error(f"An error occurred during orchestrator.process_document: {e}", exc_info=True)
        created_files = []


    print('\n--- MULTI-PASS PROCESSING OUTPUT ---')
    if created_files:
        print('Created files:')
        for f_path in created_files:
            print(f"- {f_path}")
    else:
        print("No files were created by the multi-pass process.")
    
    print(f"\nCheck output in: {os.path.abspath(output_root)}")

if __name__ == '__main__':
    # Ensure .env is loaded if OPENAI_API_KEY is used by LLM services
    try:
        from dotenv import load_dotenv
        load_dotenv()
        logger.info(".env loaded if present.")
    except ImportError:
        logger.info("dotenv package not found, skipping .env load.")
    main()
