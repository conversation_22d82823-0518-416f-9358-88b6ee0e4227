{"monster": "Extract D&D 5e monster stats including AC, HP, ability scores (STR, DEX, CON, INT, WIS, CHA), speed, saving throws, skills, damage vulnerabilities/resistances/immunities, condition immunities, senses, languages, challenge rating, and special abilities/actions. Format ability scores as an object: {\"str\": 10, \"dex\": 12, ...}. Include XP value based on challenge rating.", "spell": "Extract D&D 5e spell details including level, school, casting time, range, components (V, S, M), duration, and full description. Format damage as XdY + Z. Include higher level casting information if available. Specify spell lists (classes that can cast this spell).", "item": "Extract D&D 5e item details including type, rarity, attunement requirements (if any), weight, value, and magical properties. For weapons include damage and properties. For armor include AC bonus and type. Use item_type_dnd5e field for categorization.", "hazard": "Extract D&D 5e hazard details including detection DC, disarm DC, trigger conditions, effects, and damage (if applicable). Include save types and DCs for any effects.", "condition": "Extract D&D 5e condition details including all effects and duration information. Cover standard conditions, diseases, madness, and curses as applicable.", "class": "Extract D&D 5e class details including hit die, primary ability, saving throw proficiencies, armor/weapon proficiencies, and class features by level. Include subclass information if present.", "race": "Extract D&D 5e race details including ability score increases, size, speed, languages, and racial traits. Include subrace information if applicable.", "background": "Extract D&D 5e background details including skill proficiencies, tool proficiencies, languages, equipment, and feature. Include variant rules if mentioned.", "feat": "Extract D&D 5e feat details including prerequisites and benefits. Note any restrictions or special conditions for taking the feat.", "encounter": "Extract D&D 5e encounter details including difficulty, XP value, monsters/NPCs involved, terrain features, and tactical considerations. Include scaling information for different party sizes or levels.", "magic_item": "Extract D&D 5e magic item details including type, rarity, attunement requirements, charges/uses, and all magical properties. Include curse information if applicable."}