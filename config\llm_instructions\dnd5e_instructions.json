{"monster": "For D&D 5e monsters, extract all stat block information including: name, size, type, alignment, AC, HP, speed, ability scores (STR, DEX, CON, INT, WIS, CHA), saving throws, skills, damage vulnerabilities/resistances/immunities, condition immunities, senses, languages, challenge rating, special traits, actions, reactions, and legendary actions if applicable. Format ability scores as an object: {\"str\": 10, \"dex\": 12, ...}. Include XP value based on challenge rating. IMPORTANT: Always provide a specific name for the monster based on the content, never use generic terms like 'unknown-monster'. Use the actual creature name from the text.", "spell": "Extract D&D 5e spell details including level, school, casting time, range, components (V, S, M), duration, and full description. Format damage as XdY + Z. Include higher level casting information if available. Specify spell lists (classes that can cast this spell). IMPORTANT: Always use the actual spell name from the content, never generic terms like 'unknown-spell'.", "item": "Extract D&D 5e item details including type, rarity, attunement requirements (if any), weight, value, and magical properties. For weapons include damage and properties. For armor include AC bonus and type. Use item_type_dnd5e field for categorization. IMPORTANT: Always use the actual item name from the content, never generic terms like 'unknown-item'.", "hazard": "Extract D&D 5e hazard details including detection DC, disarm DC, trigger conditions, effects, and damage (if applicable). Include save types and DCs for any effects. IMPORTANT: Always use the actual hazard name from the content, never generic terms like 'unknown-hazard'.", "condition": "Extract D&D 5e condition details including all effects and duration information. Cover standard conditions, diseases, madness, and curses as applicable. IMPORTANT: Always use the actual condition name from the content, never generic terms like 'unknown-condition'.", "class": "Extract D&D 5e class details including hit die, primary ability, saving throw proficiencies, armor/weapon proficiencies, and class features by level. Include subclass information if present. IMPORTANT: Always use the actual class name from the content, never generic terms like 'unknown-class'.", "race": "Extract D&D 5e race details including ability score increases, size, speed, languages, and racial traits. Include subrace information if applicable. IMPORTANT: Always use the actual race name from the content, never generic terms like 'unknown-race'.", "background": "Extract D&D 5e background details including skill proficiencies, tool proficiencies, languages, equipment, and feature. Include variant rules if mentioned. IMPORTANT: Always use the actual background name from the content, never generic terms like 'unknown-background'.", "feat": "Extract D&D 5e feat details including prerequisites and benefits. Note any restrictions or special conditions for taking the feat. IMPORTANT: Always use the actual feat name from the content, never generic terms like 'unknown-feat'.", "encounter": "Extract D&D 5e encounter details including difficulty, XP value, monsters/NPCs involved, terrain features, and tactical considerations. Include scaling information for different party sizes or levels. IMPORTANT: Always use a descriptive encounter name based on the content, never generic terms like 'unknown-encounter'.", "magic_item": "Extract D&D 5e magic item details including type, rarity, attunement requirements, charges/uses, and all magical properties. Include curse information if applicable. IMPORTANT: Always use the actual magic item name from the content, never generic terms like 'unknown-magic-item'.", "npc": "For D&D 5e NPCs, extract: name, race, class (if applicable), alignment, personality traits, goals, and any stat information provided. If the NPC has monster-like stats, include those as well. IMPORTANT: Always provide a specific name for the NPC based on the content, never use generic terms like 'unknown-npc'. Use the actual character name from the text.", "adventure_section": "For adventure sections, extract the section title, description, key locations, NPCs, encounters, and plot elements. IMPORTANT: Always use the actual section name or title from the content, never generic terms like 'unknown-adventure-section'. Use descriptive names based on the section content.", "location": "For locations, extract the location name, type, description, connections to other areas, and any special features. IMPORTANT: Always use the actual location name from the content, never generic terms like 'unknown-location'. Use the specific place names mentioned in the text."}