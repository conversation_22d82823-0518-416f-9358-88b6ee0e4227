# Project Overview and Design (Map of Contents)

---

## Current Status & Focus (as of 2025-05-13)

- **Current Focus:** Refining the single-pass `MarkdownProcessorService` for robust monster/NPC extraction, schema compliance, and entity linking. Ongoing iterative prompt engineering is tracked in [`docs/plans/llm_processing_refinement_plan_20250513.md`](./plans/llm_processing_refinement_plan_20250513.md).
- **Schema Refactor:** The TTRPG content schema (`config/ttrpg_schema.json`) is in active use but still requires refactoring into universal and game-specific versions, and the addition of alternative game schemas. See [`docs/TTRPG_Markdown_Schema_and_Pipeline_Plan.md`](./TTRPG_Markdown_Schema_and_Pipeline_Plan.md) for schema design context.
- **Image Processing:** The initial `ImageProcessingService` is implemented, but the full advanced image processing and metadata enrichment strategy is still pending (see roadmap).
- **Multi-Pass Architecture:** Planning for a future multi-pass processing pipeline is underway. See [`docs/plans/multi_pass_processing_strategy.md`](./plans/multi_pass_processing_strategy.md).
- **Key Active Plans:**
    - [Project Finalization Roadmap](./PROJECT_FINALIZATION_ROADMAP.md)
    - [LLM Processing Refinement Plan](./plans/llm_processing_refinement_plan_20250513.md)
    - [Multi-Pass Processing Strategy](./plans/multi_pass_processing_strategy.md)
    - [TTRPG Markdown Schema and Pipeline Plan](./TTRPG_Markdown_Schema_and_Pipeline_Plan.md)
- **Deprecated:** [`docs/llm_prompts.md`](./llm_prompts.md) is deprecated; refer to code for current prompts.

---

## 1. Project Vision & Core Problem

**Vision:** To create a robust and efficient pipeline for converting Tabletop Role-Playing Game (TTRPG) PDF documents into high-quality, structured Markdown. This Markdown will be optimized for consumption by Large Language Models (LLMs) and Retrieval-Augmented Generation (RAG) systems.

**Core Problem Solved:** Unlocking the vast amount of TTRPG content currently siloed in PDF format, making it accessible and usable for AI-driven applications such as session planning, automated game mastering assistance, content generation, and rules lookup.

## 2. Key Goals & Benefits

*   **Enhanced LLM Comprehension:** Provide LLMs with structured, semantically rich text rather than raw, often noisy, PDF extractions.
*   **Improved RAG Performance:** Facilitate more accurate and contextually relevant information retrieval through better chunking and metadata.
*   **Structured TTRPG Data:** Enable the explicit representation and extraction of TTRPG-specific elements (e.g., monster stat blocks, spell descriptions, adventure layouts).
*   **Increased Utility:** Make TTRPG content more versatile for various AI applications.

## 3. High-Level Architecture

The pipeline processes PDFs through several distinct stages:

1.  **Ingestion:** Validates and prepares PDF files for processing.
2.  **Conversion:** Converts PDF content to Markdown and extracts images.
    *   **Primary Strategy:** Marker (potentially with LLM assistance for layout).
    *   **Alternative Strategy:** PyMuPDF4LLM.
3.  **Image Processing (Currently a separate, subsequent script execution):**
    *   The `scripts/preprocess_adventure_markdown.py` script is run after initial PDF-to-Markdown conversion.
    *   It uses the `ImageProcessingService` to analyze extracted images (linked in the raw Markdown) using vision models.
    *   Classifies images (map, artwork, logo, decorative, etc.).
    *   Removes links to "useless" images from the Markdown.
    *   Generates descriptions for "useful" images and updates their alt text in the Markdown.
    *   Outputs an "images-processed" Markdown file and an image metadata JSON file.
    *   *Future Goal:* Integrate this service more directly into the main pipeline orchestrator (see `PROJECT_FINALIZATION_ROADMAP.md`).
4.  **Processing & Schema Validation (Operates on "images-processed" Markdown):**
    *   Cleans and refines the Markdown that has undergone image processing.
    *   Validates against the TTRPG-specific content schema (`ttrpg_schema.json`).
    *   Structures content according to the schema.
5.  **Chunking:** Divides the processed Markdown (including image descriptions/metadata) into semantically relevant chunks.
6.  **Embedding:** Generates vector embeddings for each chunk.
7.  **Storage:** Stores the chunks and their embeddings in a vector database (ChromaDB) for retrieval.

```mermaid
graph LR
    A[PDF Input] --> B(Ingestion Service);
    B --> C{Conversion Service <br> (Marker / PyMuPDF4LLM)};
    C --> D_RAW[Raw Markdown + Extracted Images];
    D_RAW --> |Run preprocess_adventure_markdown.py| D_IMG_SCRIPT(Image Processing Script <br> Vision Model Analysis);
    D_IMG_SCRIPT --> E_MD[Images-Processed Markdown <br> + Image Metadata JSON];
    E_MD --> F_PROC(Processing Service <br> Schema Validation);
    F_PROC --> G_PMD[Processed TTRPG Markdown];
    G_PMD --> H_CHUNK(Chunking Service);
    H_CHUNK --> I_CHU[Content Chunks];
    I_CHU --> J_EMB(Embedding Service);
    J_EMB --> K_EMBD[Chunks with Embeddings];
    K_EMBD --> L_VDB(Vector Storage <br> ChromaDB);
```

## 4. Core Technologies & Libraries

*   **Language:** Python
*   **Primary PDF-to-Markdown Converter:** Marker
*   **Alternative PDF-to-Markdown Converter:** PyMuPDF4LLM
*   **Vector Database:** ChromaDB
*   **Embedding Models:** Sentence-Transformers (e.g., `all-MiniLM-L6-v2`)
*   **Schema Validation:** `jsonschema`
*   **Configuration:** YAML

## 5. Key Design Principles & Strategies

*   **Markdown as the Lingua Franca:** Prioritize well-structured Markdown as the intermediate format for LLM consumption due to its inherent structural clarity and semantic cues.
*   **TTRPG-Specific Schema:** Define and enforce a clear schema for common TTRPG elements (stat blocks, spells, encounters) to ensure consistency and facilitate reliable extraction.
*   **Context-Aware Chunking:** Segment Markdown based on semantic boundaries and TTRPG structures rather than arbitrary fixed sizes.
*   **Rich Metadata:** Preserve and generate comprehensive metadata (document source, structural hierarchy, TTRPG element types, original page numbers) to enhance RAG context.
*   **Modularity:** Design services with clear responsibilities for easier maintenance and extension.
*   **Configurability:** Allow key aspects of the pipeline (e.g., conversion strategy, model names) to be configured.

## 6. Map of Contents (MOC) - Key Project Documents

This document serves as a central hub. For more detailed information, please refer to the following:

**Core Design & Planning:**
*   **Detailed Design Document:** [`designDoc_pdf_To_md_benefits.md`](./designDoc_pdf_To_md_benefits.md) - The comprehensive initial design rationale and benefits analysis.
*   **Current Project Roadmap:** [`PROJECT_FINALIZATION_ROADMAP.md`](./PROJECT_FINALIZATION_ROADMAP.md) - Active tasks and plan for project completion.
*   **TTRPG Markdown Schema and Pipeline Plan:** [`TTRPG_Markdown_Schema_and_Pipeline_Plan.md`](./TTRPG_Markdown_Schema_and_Pipeline_Plan.md) - Detailed plan for schema definition, image processing, AIGM interaction, and Markdown structure.
*   **Original TTRPG PDF Conversion Design:** [`TTRPG PDF Conversion Design_.md`](./TTRPG%20PDF%20Conversion%20Design_.md) - Another early design document.

**Configuration & Schemas:**
*   **Pipeline Configuration:** [`../src/config/config.yaml`](../src/config/config.yaml) - Main YAML configuration for the pipeline.
*   **TTRPG Content Schema:** [`../config/ttrpg_schema.json`](../config/ttrpg_schema.json) - JSON schema for TTRPG content structure in Markdown.

**Implementation & Technical References:**
*   **Marker Implementation Notes:** [`ref_marker_implementation.md`](./ref_marker_implementation.md) - Specifics related to the Marker library integration.
*   **PyMuPDF4LLM Implementation Notes:** [`ref_pymupdf4llm_implementation.md`](./ref_pymupdf4llm_implementation.md) - Specifics related to the PyMuPDF4LLM library integration.

**Operational & Historical Documents:**
*   **Project Scripts & Commands:** [`PROJECT_SCRIPTS.md`](./PROJECT_SCRIPTS.md) - Useful scripts and command-line invocations.
*   **Archived Initial Roadmap:** [`archive/PROJECT_IMPLEMENTATION_STATUS_archived_20250509.md`](./archive/PROJECT_IMPLEMENTATION_STATUS_archived_20250509.md) - The initial implementation status and roadmap.
*   **Archived Plans & Handoffs:**
    *   [`plans/marker_config_refactor_plan.md`](./plans/marker_config_refactor_plan.md)
    *   [`plans/marker_metadata_error_and_link_correction_plan_v3.4.md`](./plans/marker_metadata_error_and_link_correction_plan_v3.4.md)
    *   [`handoffs/marker_image_path_correction_handoff.md`](./handoffs/marker_image_path_correction_handoff.md)

**Research & Supporting Materials:**
*   **TTRPG Markdown Organization Research:** [`TTRPG Markdown Organization Research_.md`](./TTRPG%20Markdown%20Organization%20Research_.md) - Research on structuring TTRPG content in Markdown.

**Source Code:**
*   **Main Orchestrator:** [`../src/orchestration/pipeline_orchestrator.py`](../src/orchestration/pipeline_orchestrator.py)
*   **Conversion Service:** [`../src/conversion/pdf_to_markdown_converter_service.py`](../src/conversion/pdf_to_markdown_converter_service.py)
*   **All Services & Logic:** [`../src/`](../src/)

## 7. Future Considerations (Brief)

*   Advanced OCR solutions for challenging scanned PDFs.
*   Direct integration with LLM APIs for summarization or reformatting tasks within the pipeline.
*   User interface for managing PDF ingestion and reviewing Markdown output.
